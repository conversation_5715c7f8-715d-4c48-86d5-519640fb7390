# CSV合并工具 - 数据缺失填充功能

## 新增功能

在 `csv_merger_cli.py` 中添加了数据缺失时的空值填充操作，确保数据关键点完整。

## 功能特点

1. **自动检测时间间隙**：工具会自动检测数据中缺失的时间点（按秒级别）
2. **多种填充方法**：提供5种不同的数据填充策略
3. **保持数据完整性**：确保每秒都有一个数据点，避免时间序列中的空隙
4. **保持原始时间点**：不改变原有数据的时间点，只填充缺失的时间点

## 填充方法

### 1. 空值填充 (empty) - 默认推荐
```bash
python csv_merger_cli.py --fill-method empty file1.csv file2.csv -o output.csv
```
缺失的数据点用空值填充，保持数据的真实性。

### 2. 零填充 (zero)
```bash
python csv_merger_cli.py --fill-method zero file1.csv file2.csv -o output.csv
```
缺失的数据点用 0 填充。

### 3. 线性插值 (interpolate)
```bash
python csv_merger_cli.py --fill-method interpolate file1.csv file2.csv -o output.csv
```
使用线性插值计算缺失数据点的值，提供最平滑的数据过渡。

### 4. 前向填充 (forward_fill)
```bash
python csv_merger_cli.py --fill-method forward_fill file1.csv file2.csv -o output.csv
```
用前一个有效值填充缺失的数据点。

### 5. 后向填充 (backward_fill)
```bash
python csv_merger_cli.py --fill-method backward_fill file1.csv file2.csv -o output.csv
```
用后一个有效值填充缺失的数据点。

## 使用示例

### 基本用法（使用默认空值填充）
```bash
python csv_merger_cli.py sensor1.csv sensor2.csv sensor3.csv
```

### 指定填充方法
```bash
python csv_merger_cli.py --fill-method zero sensor1.csv sensor2.csv -o merged_data.csv
```

### 查看帮助
```bash
python csv_merger_cli.py --help
```

## 技术实现

1. **时间序列完整性检查**：分析输入数据的时间范围，生成完整的每秒时间序列
2. **智能数据点选择**：对于每秒内有多个数据点的情况，选择最接近中位数的点作为关键数据点
3. **缺失数据填充**：根据选择的填充方法，为缺失的时间点生成合适的数据值
4. **数据类型保持**：确保填充后的数据保持原有的数值类型和精度

## 输出信息

工具运行时会显示：
- 使用的填充方法
- 处理的文件数量和数据点数量
- 数据关键点完整性保证信息

示例输出：
```
使用填充方法: empty
开始合并 3 个文件...
✓ 成功合并 3 个传感器文件
✓ 智能提取 300 个关键数据点（每秒一个）
✓ 数据缺失填充方法：empty
✓ 数据关键点完整性已通过空值填充保证
✓ 输出文件：华300.csv
```

## 注意事项

1. 填充功能会自动激活，确保时间序列的连续性
2. **推荐使用 `empty` 方法**保持数据真实性，缺失数据显示为空值
3. 对于需要数值计算的场景，可以使用 `interpolate` 或 `forward_fill`
4. `zero` 填充可能会影响数据分析结果，请谨慎使用
5. 工具会自动处理跨午夜的时间序列
6. 原有数据的时间点保持不变，只填充缺失的时间点
