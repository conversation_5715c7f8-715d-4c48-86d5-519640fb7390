#!/usr/bin/env python3
"""
专门用于合并WT传感器CSV文件的脚本
解决csv_merger_cli.py的数据类型问题
"""
import pandas as pd
import numpy as np
from pathlib import Path
import sys
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

def extract_key_data_points(df, time_col='Date_Time'):
    """
    按时间间隔处理数据，每秒提取关键数据点
    """
    if df.empty:
        return df
    
    print(f"  原始数据: {len(df)} 行")
    
    # 确保时间列是字符串格式
    df[time_col] = df[time_col].astype(str)
    
    # 按秒分组（只取时分秒，忽略毫秒）
    df['time_second'] = df[time_col].str[11:19]  # 提取HH:MM:SS部分
    
    # 按秒分组
    grouped = df.groupby('time_second')
    
    key_points = []
    for time_sec, group in grouped:
        if len(group) == 1:
            # 如果该秒只有一个数据点，直接使用
            key_points.append(group.iloc[0])
        else:
            # 如果该秒有多个数据点，选择最接近中位数的点
            group_sorted = group.sort_values(time_col)
            
            # 计算该秒内所有数据点的中位数
            numeric_cols = ['X_Acceler', 'Y_Acceler', 'Z_Acceler']
            medians = {}
            
            for col in numeric_cols:
                if col in group_sorted.columns:
                    # 转换为数值类型，处理混合类型问题
                    numeric_values = pd.to_numeric(group_sorted[col], errors='coerce')
                    medians[col] = numeric_values.median()
            
            # 计算每个点到中位数的距离
            distances = []
            for idx, row in group_sorted.iterrows():
                dist = 0
                for col in numeric_cols:
                    if col in medians and col in row:
                        try:
                            val = float(row[col])
                            dist += (val - medians[col])**2
                        except:
                            continue
                distances.append((np.sqrt(dist), idx))
            
            # 选择距离中位数最近的点作为关键数据点
            if distances:
                distances.sort()
                best_idx = distances[0][1]
                key_points.append(group_sorted.loc[best_idx])
    
    # 创建新的DataFrame
    result_df = pd.DataFrame(key_points)
    result_df = result_df.drop('time_second', axis=1, errors='ignore')
    
    # 按时间排序
    result_df = result_df.sort_values(time_col).reset_index(drop=True)
    
    print(f"  关键数据点: {len(result_df)} 行")
    return result_df

def fill_missing_time_points(sensor_dfs, fill_method='empty'):
    """
    填充缺失的时间点，确保所有传感器在相同时间点都有数据
    """
    if not sensor_dfs:
        return sensor_dfs
    
    # 收集所有时间点
    all_times = set()
    for sensor_name, df in sensor_dfs.items():
        if not df.empty:
            # 提取时间的HH:MM:SS部分
            times = df['Date_Time'].str[11:19].unique()
            all_times.update(times)
    
    sorted_times = sorted(all_times)
    print(f"总时间点数: {len(sorted_times)}")
    
    # 为每个传感器填充缺失时间点
    filled_sensor_dfs = {}
    for sensor_name, df in sensor_dfs.items():
        print(f"填充传感器 {sensor_name} 的缺失时间点...")
        
        # 创建时间索引
        df_indexed = df.copy()
        df_indexed['time_key'] = df_indexed['Date_Time'].str[11:19]
        df_indexed = df_indexed.set_index('time_key')
        
        # 创建完整时间序列的数据
        filled_data = []
        for time_point in sorted_times:
            if time_point in df_indexed.index:
                # 使用现有数据
                row_data = df_indexed.loc[time_point]
                if isinstance(row_data, pd.DataFrame):
                    row_data = row_data.iloc[0]  # 如果有重复，取第一个
                
                filled_data.append({
                    'Date_Time': row_data['Date_Time'],
                    'X_Acceler': row_data['X_Acceler'],
                    'Y_Acceler': row_data['Y_Acceler'],
                    'Z_Acceler': row_data['Z_Acceler']
                })
            else:
                # 创建缺失数据点
                # 构造完整的时间字符串（使用第一个数据点的日期部分）
                if not df.empty:
                    sample_time = df['Date_Time'].iloc[0]
                    date_part = sample_time[:11]  # YYYY-MM-DD 
                    full_time = date_part + time_point
                else:
                    full_time = f"2023-08-13 {time_point}"
                
                if fill_method == 'empty':
                    filled_data.append({
                        'Date_Time': full_time,
                        'X_Acceler': '',
                        'Y_Acceler': '',
                        'Z_Acceler': ''
                    })
                elif fill_method == 'zero':
                    filled_data.append({
                        'Date_Time': full_time,
                        'X_Acceler': 0,
                        'Y_Acceler': 0,
                        'Z_Acceler': 0
                    })
        
        # 创建填充后的DataFrame
        filled_df = pd.DataFrame(filled_data)
        filled_sensor_dfs[sensor_name] = filled_df
        print(f"  填充后: {len(filled_df)} 行数据")
    
    return filled_sensor_dfs

def merge_sensor_files(file_paths, output_path, fill_method='empty'):
    """
    合并多个传感器CSV文件
    """
    print(f"开始合并 {len(file_paths)} 个传感器文件...")
    print(f"填充方法: {fill_method}")
    
    # 读取并处理每个文件
    sensor_dfs = {}
    
    for i, file_path in enumerate(file_paths):
        file_name = Path(file_path).stem
        print(f"\n处理文件 {i+1}/{len(file_paths)}: {file_name}")
        
        try:
            # 读取CSV文件，指定数据类型以避免混合类型警告
            df = pd.read_csv(file_path, dtype={'Date_Time': str, 'X_Acceler': str, 'Y_Acceler': str, 'Z_Acceler': str})
            
            if df.empty:
                print(f"  警告: {file_name} 是空文件")
                continue
            
            # 提取关键数据点
            key_df = extract_key_data_points(df)
            
            if not key_df.empty:
                sensor_dfs[file_name] = key_df
            else:
                print(f"  警告: {file_name} 没有有效数据")
                
        except Exception as e:
            print(f"  错误: 处理 {file_name} 时出现异常: {e}")
            continue
    
    if not sensor_dfs:
        print("错误: 没有有效的传感器数据")
        return False
    
    # 填充缺失的时间点
    print(f"\n填充缺失时间点...")
    filled_sensor_dfs = fill_missing_time_points(sensor_dfs, fill_method)
    
    # 合并所有传感器数据
    print(f"\n合并传感器数据...")
    
    # 收集所有时间点
    all_times = set()
    for sensor_name, df in filled_sensor_dfs.items():
        if not df.empty:
            times = df['Date_Time'].str[11:19].unique()
            all_times.update(times)
    
    sorted_times = sorted(all_times)
    
    # 创建合并后的数据
    merged_data = []
    for i, time_point in enumerate(sorted_times):
        if i % 1000 == 0:
            print(f"  进度: {i}/{len(sorted_times)}")
        
        # 构造完整时间字符串
        if filled_sensor_dfs:
            sample_df = next(iter(filled_sensor_dfs.values()))
            if not sample_df.empty:
                sample_time = sample_df['Date_Time'].iloc[0]
                date_part = sample_time[:11]
                full_time = date_part + time_point
            else:
                full_time = f"2023-08-13 {time_point}"
        else:
            full_time = f"2023-08-13 {time_point}"
        
        # 创建合并行
        row = {'时间': full_time, 'Tension': ''}  # Tension暂时为空
        
        # 按WT1, WT2, WT3...的顺序添加传感器数据
        for wt_index in range(1, 7):  # WT1到WT6
            sensor_name = f"WT{wt_index}"
            
            if sensor_name in filled_sensor_dfs:
                df = filled_sensor_dfs[sensor_name]
                # 查找对应时间点的数据
                time_matches = df[df['Date_Time'].str[11:19] == time_point]
                
                if not time_matches.empty:
                    data = time_matches.iloc[0]
                    x_val = data['X_Acceler']
                    y_val = data['Y_Acceler']
                    z_val = data['Z_Acceler']
                else:
                    x_val = y_val = z_val = ''
            else:
                x_val = y_val = z_val = ''
            
            # 添加到行数据中
            row[f'WT{wt_index}_X'] = x_val
            row[f'WT{wt_index}_Y'] = y_val
            row[f'WT{wt_index}_Z'] = z_val
        
        merged_data.append(row)
    
    # 创建DataFrame并保存
    result_df = pd.DataFrame(merged_data)
    
    # 确保列的顺序正确
    ordered_columns = ['时间', 'Tension']
    for i in range(1, 7):
        ordered_columns.extend([f'WT{i}_X', f'WT{i}_Y', f'WT{i}_Z'])
    
    result_df = result_df.reindex(columns=ordered_columns, fill_value='')
    
    # 保存到CSV文件
    result_df.to_csv(output_path, index=False)
    
    print(f"\n✅ 合并完成!")
    print(f"✅ 处理了 {len(sensor_dfs)} 个传感器文件")
    print(f"✅ 生成了 {len(sorted_times)} 个时间点的数据")
    print(f"✅ 输出文件: {output_path}")
    
    return True

def main():
    """主函数"""
    if len(sys.argv) < 3:
        print("用法: python merge_wt_sensors.py <输出文件> <WT1.csv> <WT2.csv> ... [填充方法]")
        print("填充方法: empty (默认), zero")
        return
    
    output_file = sys.argv[1]
    input_files = []
    fill_method = 'empty'
    
    # 解析参数
    for arg in sys.argv[2:]:
        if arg in ['empty', 'zero']:
            fill_method = arg
        else:
            input_files.append(arg)
    
    if not input_files:
        print("错误: 请指定至少一个输入文件")
        return
    
    # 检查输入文件是否存在
    for file_path in input_files:
        if not Path(file_path).exists():
            print(f"错误: 文件 {file_path} 不存在")
            return
    
    # 合并文件
    success = merge_sensor_files(input_files, output_file, fill_method)
    
    if success:
        print(f"\n🎉 成功合并传感器数据到 {output_file}")
    else:
        print(f"\n❌ 合并失败")

if __name__ == "__main__":
    main()
