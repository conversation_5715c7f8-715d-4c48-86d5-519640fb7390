import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# 张力 是带时间处理的。输出四种数据补充方案
def extract_key_data_points_with_fill(csv_file, output_file=None, fill_method='zero',
                                     start_time=None, end_time=None):
    """
    按时间间隔处理数据，每秒提取关键数据点，并填补缺失的时间点

    参数:
    - csv_file: 输入CSV文件路径
    - output_file: 输出CSV文件路径
    - fill_method: 填补方法 ('zero', 'interpolate', 'forward_fill', 'backward_fill')
    - start_time: 开始时间 (格式: 'H:M:S')
    - end_time: 结束时间 (格式: 'H:M:S')
    """
    # 读取CSV文件
    df = pd.read_csv(csv_file)

    # 确保时间列是字符串格式
    df['Date_Time'] = df['Date_Time'].astype(str)

    # 自动检测时间范围（如果未提供）
    if start_time is None or end_time is None:
        # 解析时间并找到范围
        time_objects = []
        for time_str in df['Date_Time']:
            try:
                time_obj = datetime.strptime(time_str, '%H:%M:%S')
                time_objects.append(time_obj)
            except:
                continue

        if time_objects:
            start_time = min(time_objects).strftime('%H:%M:%S')
            end_time = max(time_objects).strftime('%H:%M:%S')
        else:
            raise ValueError("无法解析时间数据")

    print(f"处理时间范围: {start_time} - {end_time}")

    # 按秒分组（只取时分秒，忽略毫秒）
    df['time_second'] = df['Date_Time'].str[:8]  # 取前8位：HH:MM:SS

    # 按秒分组并提取关键数据点
    grouped = df.groupby('time_second')

    existing_data = {}
    for time_sec, group in grouped:
        if len(group) == 1:
            # 如果该秒只有一个数据点，直接使用
            row = group.iloc[0]
            existing_data[time_sec] = {
                'Date_Time': row['Date_Time'],
                'Tension': float(row['Tension']),
                'Speed': float(row['Speed']),
                'depth': float(row['depth'])
            }
        else:
            # 如果该秒有多个数据点，选择最优点
            group_sorted = group.sort_values('Date_Time')

            # 计算该秒内所有数据点的中位数
            tension_median = group_sorted['Tension'].median()
            speed_median = group_sorted['Speed'].median()
            depth_median = group_sorted['depth'].median()

            # 计算每个点到中位数的距离
            distances = []
            for idx, row in group_sorted.iterrows():
                dist = np.sqrt((row['Tension'] - tension_median)**2 +
                             (row['Speed'] - speed_median)**2 +
                             (row['depth'] - depth_median)**2)
                distances.append((dist, idx))

            # 选择距离中位数最近的点作为关键数据点
            distances.sort()
            best_idx = distances[0][1]
            best_row = group_sorted.loc[best_idx]
            existing_data[time_sec] = {
                'Date_Time': best_row['Date_Time'],
                'Tension': float(best_row['Tension']),
                'Speed': float(best_row['Speed']),
                'depth': float(best_row['depth'])
            }

    # 生成完整的时间序列
    start_dt = datetime.strptime(start_time, '%H:%M:%S')
    end_dt = datetime.strptime(end_time, '%H:%M:%S')

    complete_timeline = []
    current = start_dt
    while current <= end_dt:
        time_str = current.strftime('%H:%M:%S')
        # 去掉前导零以匹配原始数据格式
        if time_str.startswith('0'):
            time_str = time_str[1:]
        complete_timeline.append(time_str)
        current += timedelta(seconds=1)

    print(f"完整时间序列应有: {len(complete_timeline)} 个数据点")
    print(f"实际存在数据点: {len(existing_data)} 个")
    print(f"需要填补数据点: {len(complete_timeline) - len(existing_data)} 个")

    # 创建完整的数据集
    complete_data = []

    for time_point in complete_timeline:
        if time_point in existing_data:
            # 使用现有数据
            row = existing_data[time_point].copy()
            complete_data.append(row)
        else:
            # 创建缺失数据点
            missing_row = create_missing_data_point(time_point, existing_data,
                                                  complete_timeline, fill_method)
            complete_data.append(missing_row)

    # 创建最终DataFrame
    result_df = pd.DataFrame(complete_data)

    # 确保列的顺序正确
    column_order = ['Date_Time', 'Tension', 'Speed', 'depth']
    result_df = result_df[column_order]

    # 按时间排序
    result_df = result_df.reset_index(drop=True)

    # 输出结果
    if output_file:
        result_df.to_csv(output_file, index=False)
        print(f"完整关键数据点已保存到: {output_file}")

    print(f"最终数据点: {len(result_df)} 行")
    print(f"填补方法: {fill_method}")

    return result_df

def create_missing_data_point(time_point, existing_data, complete_timeline, fill_method):
    """
    为缺失的时间点创建数据
    """
    if fill_method == 'zero':
        # 方法1: 用0填充
        return {
            'Date_Time': time_point,
            'Tension': 0.0,
            'Speed': 0.0,
            'depth': 0.0
        }

    elif fill_method == 'interpolate':
        # 方法2: 线性插值
        return interpolate_missing_point(time_point, existing_data, complete_timeline)

    elif fill_method == 'forward_fill':
        # 方法3: 前向填充
        return forward_fill_missing_point(time_point, existing_data, complete_timeline)

    elif fill_method == 'backward_fill':
        # 方法4: 后向填充
        return backward_fill_missing_point(time_point, existing_data, complete_timeline)

    else:
        # 默认用0填充
        return {
            'Date_Time': time_point,
            'Tension': 0.0,
            'Speed': 0.0,
            'depth': 0.0
        }

def interpolate_missing_point(time_point, existing_data, complete_timeline):
    """线性插值填充缺失数据点"""
    time_idx = complete_timeline.index(time_point)

    # 找前一个有效数据点
    prev_data = None
    prev_idx = time_idx - 1
    while prev_idx >= 0:
        prev_time = complete_timeline[prev_idx]
        if prev_time in existing_data:
            prev_data = existing_data[prev_time]
            break
        prev_idx -= 1

    # 找后一个有效数据点
    next_data = None
    next_idx = time_idx + 1
    while next_idx < len(complete_timeline):
        next_time = complete_timeline[next_idx]
        if next_time in existing_data:
            next_data = existing_data[next_time]
            break
        next_idx += 1

    if prev_data is not None and next_data is not None:
        # 双向插值
        weight = (time_idx - prev_idx) / (next_idx - prev_idx)
        return {
            'Date_Time': time_point,
            'Tension': prev_data['Tension'] + weight * (next_data['Tension'] - prev_data['Tension']),
            'Speed': prev_data['Speed'] + weight * (next_data['Speed'] - prev_data['Speed']),
            'depth': prev_data['depth'] + weight * (next_data['depth'] - prev_data['depth'])
        }
    elif prev_data is not None:
        # 只有前向数据，使用前向填充
        return {
            'Date_Time': time_point,
            'Tension': prev_data['Tension'],
            'Speed': prev_data['Speed'],
            'depth': prev_data['depth']
        }
    elif next_data is not None:
        # 只有后向数据，使用后向填充
        return {
            'Date_Time': time_point,
            'Tension': next_data['Tension'],
            'Speed': next_data['Speed'],
            'depth': next_data['depth']
        }
    else:
        # 没有有效数据，用0填充
        return {
            'Date_Time': time_point,
            'Tension': 0.0,
            'Speed': 0.0,
            'depth': 0.0
        }

def forward_fill_missing_point(time_point, existing_data, complete_timeline):
    """前向填充缺失数据点"""
    time_idx = complete_timeline.index(time_point)

    # 找前一个有效数据点
    prev_idx = time_idx - 1
    while prev_idx >= 0:
        prev_time = complete_timeline[prev_idx]
        if prev_time in existing_data:
            prev_data = existing_data[prev_time]
            return {
                'Date_Time': time_point,
                'Tension': prev_data['Tension'],
                'Speed': prev_data['Speed'],
                'depth': prev_data['depth']
            }
        prev_idx -= 1

    # 没找到前向数据，用0填充
    return {
        'Date_Time': time_point,
        'Tension': 0.0,
        'Speed': 0.0,
        'depth': 0.0
    }

def backward_fill_missing_point(time_point, existing_data, complete_timeline):
    """后向填充缺失数据点"""
    time_idx = complete_timeline.index(time_point)

    # 找后一个有效数据点
    next_idx = time_idx + 1
    while next_idx < len(complete_timeline):
        next_time = complete_timeline[next_idx]
        if next_time in existing_data:
            next_data = existing_data[next_time]
            return {
                'Date_Time': time_point,
                'Tension': next_data['Tension'],
                'Speed': next_data['Speed'],
                'depth': next_data['depth']
            }
        next_idx += 1

    # 没找到后向数据，用0填充
    return {
        'Date_Time': time_point,
        'Tension': 0.0,
        'Speed': 0.0,
        'depth': 0.0
    }

# 使用示例和测试
if __name__ == '__main__':
    input_file = '西41-117-138X（9-3）.csv'

    # 测试不同的填充方法
    methods = ['zero', 'interpolate', 'forward_fill', 'backward_fill']

    print("=== 数据完整性处理测试 ===")

    for method in methods:
        print(f"\n--- 使用 {method} 方法 ---")
        output_file = f'西41-117-138X（9-3）_完整_{method}.csv'

        try:
            result = extract_key_data_points_with_fill(
                input_file,
                output_file,
                fill_method=method,
                start_time='9:40:47',
                end_time='10:51:53'
            )

            print(f"✓ 成功生成完整数据集: {output_file}")
            print(f"  数据点数量: {len(result)}")

            # 统计填充的数据点
            if method == 'zero':
                filled_count = (result['Tension'] == 0.0).sum()
                print(f"  零值填充点: {filled_count}")

        except Exception as e:
            print(f"✗ 处理失败: {str(e)}")

    print(f"\n=== 推荐使用方案 ===")
    print("1. 'zero' - 零值填充：适合明确标识缺失数据的场景")
    print("2. 'interpolate' - 线性插值：适合数据变化平滑的场景")
    print("3. 'forward_fill' - 前向填充：适合数据相对稳定的场景")
    print("4. 'backward_fill' - 后向填充：适合特殊分析需求")