#!/usr/bin/env python3
"""
填充Tension数据脚本
将4-13.csv中的TEN_CABLE值填入到目标文件的Tension列中
通过时间匹配（TIE和Date_Time）来对应数据
"""
import pandas as pd
import sys
from pathlib import Path

def parse_time_format(time_str):
    """
    解析时间格式，统一为HH:MM:SS格式
    """
    if pd.isna(time_str) or str(time_str).strip() == '':
        return ''
    
    time_str = str(time_str).strip()
    
    # 如果已经是HH:MM:SS格式
    if len(time_str) == 8 and time_str.count(':') == 2:
        return time_str
    
    # 如果是HH:MM:SS格式但长度不对，尝试补零
    if ':' in time_str:
        parts = time_str.split(':')
        if len(parts) == 3:
            # 确保每部分都是两位数
            formatted_parts = []
            for part in parts:
                if len(part) == 1:
                    formatted_parts.append('0' + part)
                else:
                    formatted_parts.append(part[:2])  # 取前两位
            return ':'.join(formatted_parts)
    
    return time_str

def load_tension_data(tension_file):
    """
    加载包含TEN_CABLE数据的文件
    """
    print(f"加载Tension数据文件: {tension_file}")
    
    try:
        df = pd.read_csv(tension_file)
        print(f"  原始数据: {len(df)} 行 {len(df.columns)} 列")
        print(f"  列名: {list(df.columns)}")
        
        # 检查必要的列是否存在
        if 'TIE' not in df.columns:
            print("  错误: 未找到TIE列")
            return None
        
        if 'TEN_CABLE' not in df.columns:
            print("  错误: 未找到TEN_CABLE列")
            return None
        
        # 处理时间格式
        df['TIE_formatted'] = df['TIE'].apply(parse_time_format)
        
        # 显示时间格式示例
        print("  TIE时间格式示例:")
        for i, (original, formatted) in enumerate(zip(df['TIE'].head(3), df['TIE_formatted'].head(3))):
            print(f"    {i+1}: {original} → {formatted}")
        
        # 创建时间到TEN_CABLE的映射
        tension_map = {}
        for _, row in df.iterrows():
            time_key = row['TIE_formatted']
            ten_cable = row['TEN_CABLE']
            
            if time_key and pd.notna(ten_cable):
                tension_map[time_key] = ten_cable
        
        print(f"  成功创建 {len(tension_map)} 个时间-张力映射")
        
        # 显示部分映射示例
        print("  时间-张力映射示例:")
        for i, (time_key, tension) in enumerate(list(tension_map.items())[:3]):
            print(f"    {time_key} → {tension}")
        
        return tension_map
        
    except Exception as e:
        print(f"  错误: 加载文件时出现异常: {e}")
        return None

def fill_tension_column(target_file, tension_map, output_file=None):
    """
    填充目标文件的Tension列
    """
    if output_file is None:
        target_path = Path(target_file)
        output_file = target_path.parent / f"{target_path.stem}_with_tension{target_path.suffix}"
    
    print(f"\n填充目标文件: {target_file}")
    
    try:
        df = pd.read_csv(target_file)
        print(f"  目标文件数据: {len(df)} 行 {len(df.columns)} 列")
        
        # 检查必要的列是否存在
        if 'Date_Time' not in df.columns:
            print("  错误: 未找到Date_Time列")
            return False
        
        if 'Tension' not in df.columns:
            print("  错误: 未找到Tension列")
            return False
        
        # 显示原始Tension列状态
        original_tension_count = df['Tension'].notna().sum()
        print(f"  原始Tension非空值: {original_tension_count} 个")
        
        # 处理Date_Time格式
        df['Date_Time_formatted'] = df['Date_Time'].apply(parse_time_format)
        
        # 显示时间格式示例
        print("  Date_Time时间格式示例:")
        for i, (original, formatted) in enumerate(zip(df['Date_Time'].head(3), df['Date_Time_formatted'].head(3))):
            print(f"    {i+1}: {original} → {formatted}")
        
        # 填充Tension数据
        filled_count = 0
        matched_count = 0
        
        for idx, row in df.iterrows():
            time_key = row['Date_Time_formatted']
            
            if time_key in tension_map:
                matched_count += 1
                # 如果原来的Tension是空的，或者我们要覆盖所有值
                if pd.isna(row['Tension']) or str(row['Tension']).strip() == '':
                    df.at[idx, 'Tension'] = tension_map[time_key]
                    filled_count += 1
        
        print(f"  时间匹配成功: {matched_count} 个")
        print(f"  Tension填充成功: {filled_count} 个")
        
        # 显示填充后的状态
        final_tension_count = df['Tension'].notna().sum()
        print(f"  填充后Tension非空值: {final_tension_count} 个")
        
        # 删除临时列
        df = df.drop('Date_Time_formatted', axis=1)
        
        # 保存结果
        df.to_csv(output_file, index=False)
        
        print(f"  ✅ 填充完成!")
        print(f"  ✅ 输入文件: {target_file}")
        print(f"  ✅ 输出文件: {output_file}")
        print(f"  ✅ 成功填充 {filled_count} 个Tension值")
        
        # 显示填充结果示例
        filled_data = df[df['Tension'].notna() & (df['Tension'] != '')].head(3)
        if not filled_data.empty:
            print("  填充结果示例:")
            for i, (_, row) in enumerate(filled_data.iterrows()):
                print(f"    {i+1}: {row['Date_Time']} → Tension: {row['Tension']}")
        
        return True
        
    except Exception as e:
        print(f"  错误: 处理文件时出现异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """
    主函数
    """
    if len(sys.argv) < 3:
        print("用法:")
        print("  python fill_tension_data.py <目标文件> <张力数据文件> [输出文件]")
        print("  例如: python fill_tension_data.py 华200_4_13_wt空_converted.csv 4-13.csv")
        print("  例如: python fill_tension_data.py 华200_4_13_wt空_converted.csv 4-13.csv 输出文件.csv")
        return
    
    target_file = sys.argv[1]
    tension_file = sys.argv[2]
    output_file = sys.argv[3] if len(sys.argv) > 3 else None
    
    # 检查文件是否存在
    if not Path(target_file).exists():
        print(f"错误: 目标文件 {target_file} 不存在")
        return
    
    if not Path(tension_file).exists():
        print(f"错误: 张力数据文件 {tension_file} 不存在")
        return
    
    print("开始填充Tension数据...")
    print("=" * 60)
    
    # 1. 加载张力数据
    tension_map = load_tension_data(tension_file)
    
    if tension_map is None:
        print("❌ 加载张力数据失败")
        return
    
    if not tension_map:
        print("❌ 没有有效的张力数据")
        return
    
    # 2. 填充目标文件
    success = fill_tension_column(target_file, tension_map, output_file)
    
    if success:
        print(f"\n🎉 成功填充Tension数据!")
    else:
        print(f"\n❌ 填充Tension数据失败")

if __name__ == "__main__":
    main()
