#!/usr/bin/env python3
"""
高性能传感器数据处理工具
按照tests.csv格式输出，支持时间序列对齐和关键点提取
优化版：使用向量化操作和并行处理提升性能
"""
import pandas as pd
import numpy as np
from pathlib import Path
from datetime import datetime, timedelta
import multiprocessing as mp
from concurrent.futures import ProcessPoolExecutor, ThreadPoolExecutor
import warnings
warnings.filterwarnings('ignore')

def parse_tests_csv(file_path):
    """
    解析CSV或Excel文件，将其转换为标准格式
    支持CSV和Excel格式的传感器数据文件
    """
    print(f"读取文件: {file_path}")

    # 检查文件格式，决定读取方式
    # 先尝试检测是否为Excel文件（通过文件头）
    try:
        with open(file_path, 'rb') as f:
            file_header = f.read(4)

        is_excel = (file_header == b'PK\x03\x04')  # Excel文件的ZIP头

    except:
        is_excel = False

    file_ext = Path(file_path).suffix.lower()

    if is_excel or file_ext in ['.xlsx', '.xls']:
        # 读取Excel文件
        try:
            df = pd.read_excel(file_path, header=None)
            print(f"成功读取Excel文件，共 {len(df)} 行 {len(df.columns)} 列")

            # 显示前几行数据以了解格式
            print("文件前5行内容：")
            for i in range(min(5, len(df))):
                print(f"第{i+1}行: {df.iloc[i].tolist()}")

            # 转换为字符串列表格式，便于后续处理
            data_lines = []
            for i in range(len(df)):
                row_data = df.iloc[i].fillna('').astype(str).tolist()
                data_lines.append(','.join(row_data))

        except Exception as e:
            raise ValueError(f"无法读取Excel文件 {file_path}: {e}")

    else:
        # 读取CSV文件
        encodings = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig', 'latin1']
        lines = None

        for encoding in encodings:
            try:
                with open(file_path, 'r', encoding=encoding) as f:
                    lines = f.readlines()
                print(f"成功使用 {encoding} 编码读取CSV文件")
                break
            except UnicodeDecodeError:
                continue

        if lines is None:
            raise ValueError(f"无法读取文件 {file_path}，尝试了多种编码方式都失败")

        # 跳过前两行标题（如果是原来的tests.csv格式）
        data_lines = [line.strip() for line in lines[2:] if line.strip()]
    
    # 解析每个传感器的数据
    sensors_data = {}

    if is_excel or file_ext in ['.xlsx', '.xls']:
        # 处理Excel格式数据
        # 从第3行开始处理数据（跳过标题行）
        for row_idx in range(2, len(df)):  # 从第3行开始（索引2）
            row_data = df.iloc[row_idx]

            # 解析6个传感器的数据
            for sensor_idx in range(6):
                sensor_name = f"WT{sensor_idx + 1}"

                # 每个传感器的列索引（时间、X、Y、Z）
                if sensor_idx == 0:
                    time_col, x_col, y_col, z_col = 0, 1, 2, 3
                elif sensor_idx == 1:
                    time_col, x_col, y_col, z_col = 5, 6, 7, 8
                elif sensor_idx == 2:
                    time_col, x_col, y_col, z_col = 10, 11, 12, 13
                elif sensor_idx == 3:
                    time_col, x_col, y_col, z_col = 15, 16, 17, 18
                elif sensor_idx == 4:
                    time_col, x_col, y_col, z_col = 20, 21, 22, 23
                elif sensor_idx == 5:
                    time_col, x_col, y_col, z_col = 25, 26, 27, 28

                # 检查列索引是否有效
                if z_col < len(row_data):
                    time_val = row_data.iloc[time_col] if time_col < len(row_data) else None
                    x_val = row_data.iloc[x_col] if x_col < len(row_data) else None
                    y_val = row_data.iloc[y_col] if y_col < len(row_data) else None
                    z_val = row_data.iloc[z_col] if z_col < len(row_data) else None

                    # 检查数据有效性
                    if (time_val is not None and not pd.isna(time_val) and
                        x_val is not None and not pd.isna(x_val) and
                        y_val is not None and not pd.isna(y_val) and
                        z_val is not None and not pd.isna(z_val)):

                        if sensor_name not in sensors_data:
                            sensors_data[sensor_name] = []

                        # 处理时间格式
                        if hasattr(time_val, 'strftime'):
                            time_str = time_val.strftime('%Y-%m-%d %H:%M:%S')
                        else:
                            time_str = str(time_val)

                        try:
                            sensors_data[sensor_name].append({
                                '时间': time_str,
                                'X': float(x_val),
                                'Y': float(y_val),
                                'Z': float(z_val)
                            })
                        except (ValueError, TypeError):
                            # 跳过无法转换的数据
                            continue

    else:
        # 处理CSV格式数据
        for line in data_lines:
            parts = line.split(',')
            if len(parts) < 24:  # 6个传感器 * 4列 = 24列
                continue

            # 解析6个传感器的数据
            for sensor_idx in range(6):
                sensor_name = f"WT{sensor_idx + 1}"
                base_idx = sensor_idx * 5  # 每个传感器占5列（时间,X,Y,Z,空列）

                if base_idx + 3 < len(parts):
                    time_str = parts[base_idx].strip()
                    x_val = parts[base_idx + 1].strip()
                    y_val = parts[base_idx + 2].strip()
                    z_val = parts[base_idx + 3].strip()

                    if time_str and x_val and y_val and z_val:
                        if sensor_name not in sensors_data:
                            sensors_data[sensor_name] = []

                        try:
                            sensors_data[sensor_name].append({
                                '时间': time_str,
                                'X': float(x_val),
                                'Y': float(y_val),
                                'Z': float(z_val)
                            })
                        except (ValueError, TypeError):
                            # 跳过无法转换的数据
                            continue
    
    # 转换为DataFrame
    sensor_dfs = {}
    for sensor_name, data in sensors_data.items():
        if data:
            df = pd.DataFrame(data)
            sensor_dfs[sensor_name] = df
            print(f"  {sensor_name}: {len(df)} 行数据")
    
    return sensor_dfs

def extract_key_data_points(df, fill_method='empty'):
    """
    按时间间隔处理数据，每秒提取关键数据点
    参考csv_merger_cli.py中的extract_key_data_points函数
    """
    if df.empty:
        return df
    
    # 确保时间列是字符串格式
    df['时间'] = df['时间'].astype(str)
    
    # 按秒分组（只取时分秒，忽略毫秒）
    df['time_second'] = df['时间'].str[11:19]  # 提取HH:MM:SS部分
    
    # 按秒分组
    grouped = df.groupby('time_second')
    
    key_points = []
    for time_sec, group in grouped:
        if len(group) == 1:
            # 如果该秒只有一个数据点，直接使用
            key_points.append(group.iloc[0])
        else:
            # 如果该秒有多个数据点，选择最接近中位数的点
            group_sorted = group.sort_values('时间')
            
            # 计算该秒内所有数据点的中位数
            x_median = group_sorted['X'].median()
            y_median = group_sorted['Y'].median()
            z_median = group_sorted['Z'].median()
            
            # 计算每个点到中位数的距离
            distances = []
            for idx, row in group_sorted.iterrows():
                dist = np.sqrt((row['X'] - x_median)**2 +
                             (row['Y'] - y_median)**2 +
                             (row['Z'] - z_median)**2)
                distances.append((dist, idx))
            
            # 选择距离中位数最近的点作为关键数据点
            distances.sort()
            best_idx = distances[0][1]
            key_points.append(group_sorted.loc[best_idx])
    
    # 创建新的DataFrame
    result_df = pd.DataFrame(key_points)
    result_df = result_df.drop('time_second', axis=1, errors='ignore')
    
    # 按时间排序
    result_df = result_df.sort_values('时间').reset_index(drop=True)
    
    return result_df

def fill_missing_time_points(sensor_dfs, fill_method='empty'):
    """
    填充缺失的时间点，确保所有传感器在相同时间点都有数据
    参考csv_merger_cli.py中的fill_missing_data_points函数
    """
    if not sensor_dfs:
        return sensor_dfs
    
    # 收集所有时间点
    all_times = set()
    for sensor_name, df in sensor_dfs.items():
        if not df.empty:
            # 提取时间的HH:MM:SS部分
            times = df['时间'].str[11:19].unique()
            all_times.update(times)
    
    sorted_times = sorted(all_times)
    print(f"总时间点数: {len(sorted_times)}")
    
    # 为每个传感器填充缺失时间点
    filled_sensor_dfs = {}
    for sensor_name, df in sensor_dfs.items():
        print(f"处理传感器 {sensor_name}...")
        
        # 创建时间索引
        df_indexed = df.copy()
        df_indexed['time_key'] = df_indexed['时间'].str[11:19]
        df_indexed = df_indexed.set_index('time_key')
        
        # 创建完整时间序列的数据
        filled_data = []
        for time_point in sorted_times:
            if time_point in df_indexed.index:
                # 使用现有数据
                row_data = df_indexed.loc[time_point]
                if isinstance(row_data, pd.DataFrame):
                    row_data = row_data.iloc[0]  # 如果有重复，取第一个
                
                filled_data.append({
                    '时间': row_data['时间'],
                    'X': row_data['X'],
                    'Y': row_data['Y'],
                    'Z': row_data['Z']
                })
            else:
                # 创建缺失数据点
                # 构造完整的时间字符串（使用第一个数据点的日期部分）
                if not df.empty:
                    sample_time = df['时间'].iloc[0]
                    date_part = sample_time[:11]  # YYYY-MM-DD 
                    full_time = date_part + time_point
                else:
                    full_time = f"2023-08-13 {time_point}"
                
                if fill_method == 'empty':
                    filled_data.append({
                        '时间': full_time,
                        'X': '',
                        'Y': '',
                        'Z': ''
                    })
                elif fill_method == 'zero':
                    filled_data.append({
                        '时间': full_time,
                        'X': 0,
                        'Y': 0,
                        'Z': 0
                    })
                else:  # interpolate等其他方法先用NaN
                    filled_data.append({
                        '时间': full_time,
                        'X': np.nan,
                        'Y': np.nan,
                        'Z': np.nan
                    })
        
        # 创建填充后的DataFrame
        filled_df = pd.DataFrame(filled_data)
        
        # 应用插值等填充方法
        if fill_method == 'interpolate':
            for col in ['X', 'Y', 'Z']:
                filled_df[col] = pd.to_numeric(filled_df[col], errors='coerce')
                filled_df[col] = filled_df[col].interpolate(method='linear')
                filled_df[col] = filled_df[col].fillna('')
        elif fill_method == 'forward_fill':
            for col in ['X', 'Y', 'Z']:
                filled_df[col] = pd.to_numeric(filled_df[col], errors='coerce')
                filled_df[col] = filled_df[col].ffill().fillna('')
        elif fill_method == 'backward_fill':
            for col in ['X', 'Y', 'Z']:
                filled_df[col] = pd.to_numeric(filled_df[col], errors='coerce')
                filled_df[col] = filled_df[col].bfill().fillna('')
        
        filled_sensor_dfs[sensor_name] = filled_df
        print(f"  填充后: {len(filled_df)} 行数据")
    
    return filled_sensor_dfs

def process_sensor_alignment(args):
    """
    并行处理单个传感器的时间对齐
    """
    sensor_name, df_sorted, time_range = args

    aligned_data = []
    df_indexed = df_sorted.set_index('datetime')

    # 向量化处理：批量查找时间点
    for time_point in time_range:
        second_start = time_point
        second_end = time_point + pd.Timedelta(seconds=1)

        # 查找该秒内的所有数据点
        mask = (df_indexed.index >= second_start) & (df_indexed.index < second_end)
        second_data = df_indexed[mask]

        if not second_data.empty:
            if len(second_data) == 1:
                best_row = second_data.iloc[0]
            else:
                # 向量化计算中位数和距离
                x_median = second_data['X'].median()
                y_median = second_data['Y'].median()
                z_median = second_data['Z'].median()

                # 向量化计算距离
                distances = np.sqrt((second_data['X'] - x_median)**2 +
                                  (second_data['Y'] - y_median)**2 +
                                  (second_data['Z'] - z_median)**2)

                best_idx = distances.idxmin()
                best_row = second_data.loc[best_idx]

            aligned_data.append({
                'time_point': time_point,
                'X': best_row['X'],
                'Y': best_row['Y'],
                'Z': best_row['Z']
            })
        else:
            aligned_data.append({
                'time_point': time_point,
                'X': '',
                'Y': '',
                'Z': ''
            })

    return sensor_name, aligned_data

def merge_sensor_data(sensor_dfs, output_path='merged_tests.csv'):
    """
    高性能合并传感器数据，输出tests.csv格式
    使用并行处理和向量化操作优化性能
    """
    print("开始高性能时间序列对齐和数据合并...")

    # 第一步：收集所有传感器的完整时间序列
    all_sensor_times = {}
    global_time_range = {'start': None, 'end': None}

    for sensor_name, df in sensor_dfs.items():
        if not df.empty:
            # 向量化时间转换
            df_copy = df.copy()
            df_copy['datetime'] = pd.to_datetime(df_copy['时间'])
            df_sorted = df_copy.sort_values('datetime')

            sensor_start = df_sorted['datetime'].min()
            sensor_end = df_sorted['datetime'].max()

            print(f"{sensor_name}: {len(df)} 个数据点, 时间范围: {sensor_start} 到 {sensor_end}")

            # 更新全局时间范围
            if global_time_range['start'] is None or sensor_start < global_time_range['start']:
                global_time_range['start'] = sensor_start
            if global_time_range['end'] is None or sensor_end > global_time_range['end']:
                global_time_range['end'] = sensor_end

            all_sensor_times[sensor_name] = df_sorted

    print(f"全局时间范围: {global_time_range['start']} 到 {global_time_range['end']}")

    # 第二步：生成完整的时间序列（每秒一个点）
    start_time = global_time_range['start']
    end_time = global_time_range['end']

    time_range = pd.date_range(start=start_time.replace(microsecond=0),
                              end=end_time.replace(microsecond=0),
                              freq='1S')

    print(f"生成完整时间序列: {len(time_range)} 个时间点")

    # 第三步：使用并行处理对齐传感器数据
    print("使用并行处理对齐传感器数据...")

    # 准备并行处理参数
    process_args = [(sensor_name, df_sorted, time_range)
                   for sensor_name, df_sorted in all_sensor_times.items()]

    # 使用线程池并行处理（I/O密集型任务）
    aligned_sensor_data = {}
    with ThreadPoolExecutor(max_workers=min(6, len(process_args))) as executor:
        results = list(executor.map(process_sensor_alignment, process_args))

    for sensor_name, aligned_data in results:
        aligned_sensor_data[sensor_name] = aligned_data
        print(f"  {sensor_name} 对齐完成: {len(aligned_data)} 个时间点")

    # 第四步：合并所有传感器的对齐数据
    print("合并对齐后的传感器数据...")
    merged_data = []

    for i, time_point in enumerate(time_range):
        if i % 1000 == 0:
            print(f"  进度: {i}/{len(time_range)}")

        time_str = time_point.strftime('%Y-%m-%d %H:%M:%S')

        # 创建符合目标格式的行
        row = {'时间': time_str, 'Tension': ''}  # Tension暂时为空

        # 按WT1, WT2, WT3...的顺序添加传感器数据
        for wt_index in range(1, 7):  # WT1到WT6
            sensor_name = f"WT{wt_index}"

            if sensor_name in aligned_sensor_data:
                sensor_df = aligned_sensor_data[sensor_name]
                # 查找对应时间点的数据
                time_matches = sensor_df[sensor_df['时间'] == time_str]

                if not time_matches.empty:
                    data = time_matches.iloc[0]
                    x_val = data['X']
                    y_val = data['Y']
                    z_val = data['Z']
                else:
                    x_val = y_val = z_val = ''
            else:
                x_val = y_val = z_val = ''

            # 添加到行数据中
            row[f'WT{wt_index}_X'] = x_val
            row[f'WT{wt_index}_Y'] = y_val
            row[f'WT{wt_index}_Z'] = z_val

        merged_data.append(row)
    
    # 创建DataFrame并保存
    result_df = pd.DataFrame(merged_data)
    
    # 确保列的顺序正确
    ordered_columns = ['时间', 'Tension']
    for i in range(1, 7):
        ordered_columns.extend([f'WT{i}_X', f'WT{i}_Y', f'WT{i}_Z'])
    
    result_df = result_df.reindex(columns=ordered_columns, fill_value='')
    
    # 保存到CSV文件
    result_df.to_csv(output_path, index=False)
    
    print(f"\n✓ 成功处理Excel/CSV文件")
    print(f"✓ 合并了6个传感器的数据")
    print(f"✓ 时间序列对齐并智能提取了{len(time_range)}个关键数据点（每秒一个）")
    print(f"✓ 输出格式：时间 + Tension(空) + 6个传感器的XYZ数据")
    print(f"✓ 输出文件：{output_path}")
    
    return result_df

def main():
    """主函数"""
    input_file = '/Users/<USER>/Desktop/5口井的完整数据/23年绞车振动与张力数据/113-8-13/8-13.csv'
    output_file = '/Users/<USER>/Desktop/5口井的完整数据/23年绞车振动与张力数据/113-8-13/8-13全.csv'
    fill_method = 'empty'  # 可选：'empty', 'zero', 'interpolate', 'forward_fill', 'backward_fill'
    
    print(f"开始处理 {input_file}...")
    print(f"填充方法: {fill_method}")
    
    # 检查输入文件是否存在
    if not Path(input_file).exists():
        print(f"错误：文件 {input_file} 不存在")
        return
    
    try:
        # 1. 解析tests.csv文件
        sensor_dfs = parse_tests_csv(input_file)
        
        if not sensor_dfs:
            print("错误：未能解析到有效的传感器数据")
            return
        
        # 2. 直接进行时间序列对齐和数据合并（包含关键点提取）
        result_df = merge_sensor_data(sensor_dfs, output_file)
        
        print(f"\n处理完成！输出文件：{output_file}")
        
    except Exception as e:
        print(f"错误：{str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
