#!/usr/bin/env python3
"""
Excel传感器数据拆分工具
将Excel文件中的6个传感器数据拆分成独立的CSV文件
自动识别WT1、WT2...WT6表头并以此命名输出文件
"""
import pandas as pd
import numpy as np
from pathlib import Path
import sys
import warnings
warnings.filterwarnings('ignore')

def analyze_excel_structure(file_path):
    """
    分析Excel文件结构，识别传感器表头位置
    """
    print(f"分析Excel文件结构: {file_path}")
    
    try:
        # 读取Excel文件的前几行来分析结构
        df = pd.read_excel(file_path, header=None, nrows=10)
        print(f"文件尺寸: {df.shape}")
        
        # 显示前几行内容
        print("\n前5行内容:")
        for i in range(min(5, len(df))):
            row_data = df.iloc[i].fillna('').astype(str).tolist()
            print(f"第{i+1}行: {row_data}")
        
        # 查找包含WT1, WT2等标识的行
        sensor_headers = {}
        for row_idx in range(len(df)):
            row_data = df.iloc[row_idx].fillna('').astype(str)
            
            for col_idx, cell_value in enumerate(row_data):
                cell_str = str(cell_value).upper().strip()
                if cell_str.startswith('WT') and len(cell_str) <= 4:
                    # 提取传感器编号
                    try:
                        sensor_num = int(cell_str[2:])
                        if 1 <= sensor_num <= 6:
                            sensor_name = f"WT{sensor_num}"
                            if sensor_name not in sensor_headers:
                                sensor_headers[sensor_name] = []
                            sensor_headers[sensor_name].append((row_idx, col_idx))
                    except:
                        continue
        
        print(f"\n发现的传感器标识: {sensor_headers}")
        return sensor_headers
        
    except Exception as e:
        print(f"分析文件结构时出错: {e}")
        return {}

def extract_sensor_data(file_path, sensor_headers):
    """
    根据传感器表头位置提取各传感器的数据
    """
    print("开始提取传感器数据...")
    
    # 读取完整的Excel文件
    df = pd.read_excel(file_path, header=None)
    print(f"完整文件尺寸: {df.shape}")
    
    sensor_data = {}
    
    for sensor_name in sorted(sensor_headers.keys()):
        positions = sensor_headers[sensor_name]
        print(f"\n处理传感器 {sensor_name}...")
        
        # 使用第一个找到的位置
        header_row, header_col = positions[0]
        print(f"  表头位置: 第{header_row+1}行, 第{header_col+1}列")
        
        # 确定数据列的范围（通常是时间、X、Y、Z四列）
        data_columns = []
        
        # 检查表头行的下一行，确定列标题
        if header_row + 1 < len(df):
            next_row = df.iloc[header_row + 1]
            
            # 从传感器标识列开始，查找时间、X、Y、Z列
            for offset in range(5):  # 最多检查5列
                col_idx = header_col + offset
                if col_idx < len(next_row):
                    cell_value = str(next_row.iloc[col_idx]).strip()
                    if cell_value and cell_value != 'nan':
                        data_columns.append((col_idx, cell_value))
                        if len(data_columns) >= 4:  # 时间、X、Y、Z
                            break
        
        print(f"  数据列: {data_columns}")
        
        # 提取数据（从表头后第2行开始）
        data_start_row = header_row + 2
        sensor_rows = []

        if data_columns and data_start_row < len(df):
            # 限制处理行数以提高性能，并添加调试信息
            max_rows_to_process = min(len(df), data_start_row + 10000)  # 最多处理10000行数据
            print(f"  处理数据行范围: {data_start_row} 到 {max_rows_to_process}")

            for row_idx in range(data_start_row, max_rows_to_process):
                if row_idx % 5000 == 0:  # 每5000行显示一次进度
                    print(f"    进度: {row_idx - data_start_row}/{max_rows_to_process - data_start_row}")

                row_data = df.iloc[row_idx]
                
                # 提取该传感器的数据
                sensor_row = {}
                valid_data = False
                
                for i, (col_idx, col_name) in enumerate(data_columns):
                    if col_idx < len(row_data):
                        cell_value = row_data.iloc[col_idx]
                        
                        # 处理时间列
                        if i == 0:  # 第一列通常是时间
                            if pd.notna(cell_value) and str(cell_value).strip():
                                if hasattr(cell_value, 'strftime'):
                                    # 只保留时分秒，去掉年月日
                                    sensor_row['Date_Time'] = cell_value.strftime('%H:%M:%S')
                                else:
                                    # 如果是字符串格式的时间，尝试解析并提取时分秒
                                    time_str = str(cell_value)
                                    try:
                                        # 尝试解析完整的日期时间字符串
                                        dt = pd.to_datetime(time_str)
                                        sensor_row['Date_Time'] = dt.strftime('%H:%M:%S')
                                    except:
                                        # 如果解析失败，直接使用原字符串
                                        sensor_row['Date_Time'] = time_str
                                valid_data = True
                            else:
                                sensor_row['Date_Time'] = ''
                        else:
                            # 数据列（X、Y、Z）改为X_Acceler、Y_Acceler、Z_Acceler
                            col_key = ['X_Acceler', 'Y_Acceler', 'Z_Acceler'][i-1] if i <= 3 else f'Col{i}'
                            if pd.notna(cell_value) and str(cell_value).strip() and str(cell_value) != 'nan':
                                try:
                                    sensor_row[col_key] = float(cell_value)
                                    valid_data = True
                                except:
                                    sensor_row[col_key] = str(cell_value)
                            else:
                                sensor_row[col_key] = ''
                
                # 只保存有效数据行（放宽条件）
                if valid_data and sensor_row.get('Date_Time'):
                    sensor_rows.append(sensor_row)
                elif row_idx < data_start_row + 10:  # 显示前10行的调试信息
                    print(f"    跳过第{row_idx+1}行: valid_data={valid_data}, Date_Time='{sensor_row.get('Date_Time', '')}'")
                    if sensor_row:
                        print(f"      行内容: {sensor_row}")
        
        if sensor_rows:
            sensor_df = pd.DataFrame(sensor_rows)
            sensor_data[sensor_name] = sensor_df
            print(f"  提取了 {len(sensor_df)} 行数据")
            
            # 显示前几行数据示例
            print("  数据示例:")
            print(sensor_df.head(3).to_string(index=False))
        else:
            print(f"  未找到有效数据")
    
    return sensor_data

def save_sensor_csv_files(sensor_data, output_dir="."):
    """
    将各传感器数据保存为独立的CSV文件
    """
    print(f"\n保存传感器数据到CSV文件...")
    output_path = Path(output_dir)
    output_path.mkdir(exist_ok=True)
    
    saved_files = []
    
    for sensor_name, df in sensor_data.items():
        if not df.empty:
            # 生成输出文件名
            output_file = output_path / f"{sensor_name}.csv"
            
            # 保存CSV文件
            df.to_csv(output_file, index=False)
            saved_files.append(output_file)
            
            print(f"✅ {sensor_name}: {len(df)} 行数据 -> {output_file}")
    
    return saved_files

def process_excel_file(input_file, output_dir="."):
    """
    处理Excel文件的主函数
    """
    input_path = Path(input_file)
    
    if not input_path.exists():
        print(f"错误: 文件 {input_file} 不存在")
        return False
    
    print(f"开始处理Excel文件: {input_file}")
    print("=" * 60)
    
    try:
        # 1. 分析文件结构
        sensor_headers = analyze_excel_structure(input_file)
        
        if not sensor_headers:
            print("错误: 未找到传感器表头（WT1, WT2, ...）")
            return False
        
        # 2. 提取传感器数据
        sensor_data = extract_sensor_data(input_file, sensor_headers)
        
        if not sensor_data:
            print("错误: 未能提取到有效的传感器数据")
            return False
        
        # 3. 保存CSV文件
        saved_files = save_sensor_csv_files(sensor_data, output_dir)
        
        print("\n" + "=" * 60)
        print(f"🎉 处理完成!")
        print(f"📁 输入文件: {input_file}")
        print(f"📁 输出目录: {output_dir}")
        print(f"📊 成功拆分: {len(saved_files)} 个传感器文件")
        
        for file_path in saved_files:
            print(f"   - {file_path.name}")
        
        return True
        
    except Exception as e:
        print(f"处理过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """
    主函数
    """
    if len(sys.argv) < 2:
        # 默认处理当前目录下的8-13.xlsx文件
        default_file = "/Users/<USER>/Desktop/5口井的完整数据/23年绞车振动与张力数据/113-8-13/8-13.xlsx"
        if Path(default_file).exists():
            print(f"未指定文件，使用默认文件: {default_file}")
            process_excel_file(default_file)
        else:
            print("用法:")
            print("  python split_excel_sensors.py <Excel文件路径> [输出目录]")
            print("  或者将8-13.xlsx放在当前目录下直接运行")
            return
    else:
        input_file = sys.argv[1]
        output_dir = sys.argv[2] if len(sys.argv) > 2 else "."
        
        process_excel_file(input_file, output_dir)

if __name__ == "__main__":
    main()
