#!/usr/bin/env python3
"""
简化版WT传感器合并工具
直接合并WT1-WT6的CSV文件，输出华300.csv格式
"""
import pandas as pd
import numpy as np
from pathlib import Path
import sys

def read_wt_file(file_path):
    """
    读取单个WT传感器文件
    """
    try:
        df = pd.read_csv(file_path, dtype={'Date_Time': str})
        print(f"  读取 {Path(file_path).name}: {len(df)} 行数据")
        
        # 确保数值列是数值类型
        for col in ['X_Acceler', 'Y_Acceler', 'Z_Acceler']:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
        
        # 按时间排序
        df = df.sort_values('Date_Time').reset_index(drop=True)
        
        return df
    except Exception as e:
        print(f"  错误读取 {file_path}: {e}")
        return pd.DataFrame()

def extract_key_points_simple(df):
    """
    简化版关键点提取：每秒取第一个数据点
    """
    if df.empty:
        return df
    
    # 按秒分组
    df['time_second'] = df['Date_Time'].str[11:19]  # HH:MM:SS
    
    # 每秒取第一个数据点
    key_points = df.groupby('time_second').first().reset_index()
    key_points = key_points.drop('time_second', axis=1)
    
    print(f"    关键点: {len(key_points)} 行")
    return key_points

def merge_wt_files_simple(wt_files, output_file):
    """
    简化版合并WT文件
    """
    print("开始简化合并...")
    
    # 读取所有WT文件
    wt_data = {}
    for i in range(1, 7):
        wt_name = f"WT{i}"
        file_path = f"WT{i}.csv"
        
        if file_path in wt_files:
            print(f"处理 {wt_name}...")
            df = read_wt_file(file_path)
            if not df.empty:
                key_df = extract_key_points_simple(df)
                if not key_df.empty:
                    wt_data[wt_name] = key_df
    
    if not wt_data:
        print("错误：没有有效数据")
        return False
    
    # 收集所有时间点
    all_times = set()
    for wt_name, df in wt_data.items():
        times = df['Date_Time'].str[11:19].unique()
        all_times.update(times)
    
    sorted_times = sorted(all_times)
    print(f"总时间点: {len(sorted_times)}")
    
    # 构建合并数据
    merged_rows = []
    
    for i, time_point in enumerate(sorted_times):
        if i % 1000 == 0:
            print(f"  进度: {i}/{len(sorted_times)}")
        
        # 构造完整时间
        if wt_data:
            sample_df = next(iter(wt_data.values()))
            sample_time = sample_df['Date_Time'].iloc[0]
            date_part = sample_time[:11]  # YYYY-MM-DD 
            full_time = date_part + time_point
        else:
            full_time = f"2023-08-13 {time_point}"
        
        # 创建行数据
        row = {'时间': full_time, 'Tension': ''}
        
        # 添加每个传感器的数据
        for wt_idx in range(1, 7):
            wt_name = f"WT{wt_idx}"
            
            if wt_name in wt_data:
                df = wt_data[wt_name]
                # 查找对应时间的数据
                matches = df[df['Date_Time'].str[11:19] == time_point]
                
                if not matches.empty:
                    data = matches.iloc[0]
                    x_val = data['X_Acceler'] if pd.notna(data['X_Acceler']) else ''
                    y_val = data['Y_Acceler'] if pd.notna(data['Y_Acceler']) else ''
                    z_val = data['Z_Acceler'] if pd.notna(data['Z_Acceler']) else ''
                else:
                    x_val = y_val = z_val = ''
            else:
                x_val = y_val = z_val = ''
            
            row[f'WT{wt_idx}_X'] = x_val
            row[f'WT{wt_idx}_Y'] = y_val
            row[f'WT{wt_idx}_Z'] = z_val
        
        merged_rows.append(row)
    
    # 创建DataFrame
    result_df = pd.DataFrame(merged_rows)
    
    # 确保列顺序
    columns = ['时间', 'Tension']
    for i in range(1, 7):
        columns.extend([f'WT{i}_X', f'WT{i}_Y', f'WT{i}_Z'])
    
    result_df = result_df.reindex(columns=columns, fill_value='')
    
    # 保存文件
    result_df.to_csv(output_file, index=False)
    
    print(f"\n✅ 合并完成!")
    print(f"✅ 处理了 {len(wt_data)} 个传感器")
    print(f"✅ 生成了 {len(sorted_times)} 个时间点")
    print(f"✅ 输出文件: {output_file}")
    
    return True

def main():
    """主函数"""
    # 默认处理当前目录的WT1-WT6文件
    wt_files = []
    output_file = "113_8_12_235840_wt空.csv"
    
    if len(sys.argv) > 1:
        output_file = sys.argv[1]
    
    # 查找WT文件
    for i in range(1, 7):
        wt_file = f"WT{i}.csv"
        if Path(wt_file).exists():
            wt_files.append(wt_file)
        else:
            print(f"警告: {wt_file} 不存在")
    
    if not wt_files:
        print("错误: 没有找到WT文件")
        return
    
    print(f"找到 {len(wt_files)} 个WT文件")
    
    # 合并文件
    success = merge_wt_files_simple(wt_files, output_file)
    
    if success:
        print(f"\n🎉 成功合并到 {output_file}")
    else:
        print(f"\n❌ 合并失败")

if __name__ == "__main__":
    main()
