import pandas as pd
from datetime import datetime

def filter_csv_by_time(input_file, output_file, start_time, end_time):
    # 读取CSV文件
    df = pd.read_csv(input_file)
    
    # 将时间字符串转换为datetime对象以便比较
    df['Date_Time'] = pd.to_datetime(df['Date_Time'], format='%H:%M:%S').dt.time
    start_time = datetime.strptime(start_time, '%H:%M:%S').time()
    end_time = datetime.strptime(end_time, '%H:%M:%S').time()
    
    # 根据时间范围筛选数据
    filtered_df = df[(df['Date_Time'] >= start_time) & (df['Date_Time'] <= end_time)]
    
    # 将结果保存到新文件
    filtered_df.to_csv(output_file, index=False)
    print(f'已将筛选后的数据保存到: {output_file}')

# 使用示例
if __name__ == '__main__':
    # input_file = '135xwt.csv'
    input_file = '/Users/<USER>/Desktop/数据整理/datadeal/华200_4_13_wt空_converteds.csv'
    # input_file = '2024-09-03-WT3.csv'
    # input_file = 'ten-2025-02-16-WT5.csv'
    # input_file = 'ten-2025-02-16-WT6.csv'
      # 输入文件名
    output_file = '华200_4_13_空.csv'   # 输出文件名
    start_time = '11:10:03'  # 开始时间
    end_time = '14:48:10'    # 结束时间

    # output_file = '2024-09-03-WT2s.csv'   # 输出文件名
    # start_time = '2:45:12'  # 开始时间
    # end_time = '3:51:57'    # 结束时间

    # output_file = '2024-09-03-WT3s.csv'   # 输出文件名
    # start_time = '2:45:12'  # 开始时间
    # end_time = '3:51:57'    # 结束时间

    # output_file = './IMG_20250216_161406（9m54s）/WT1_20250216_161406.csv'   # 输出文件名
    # start_time = '16:09:54'  # 开始时间
    # end_time = '16:14:06'

    # output_file = './IMG_20250216_163527（14m10s）/WT1_20250216_163527.csv'   # 输出文件名
    # start_time = '16:14:10'  # 开始时间6_16455
    # end_time = '16:35:27'

    # output_file = './IMG_20250216_164559/WT1_20250216_164559.csv'   # 输出文件名
    # start_time = '16:40:02'  # 开始时间
    # end_time = '16:45:59'
    
    filter_csv_by_time(input_file, output_file, start_time, end_time)