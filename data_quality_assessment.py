#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据质量评估脚本
对插值后的数据进行全面的真实性和完整性评估
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from scipy import stats
import seaborn as sns
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def load_and_compare_data():
    """加载原始数据和插值后数据进行对比"""
    print("=== 数据加载与对比 ===")
    
    # 加载原始数据
    df_original = pd.read_csv('138x_8_3.csv', header=None)
    print(f"原始数据: {len(df_original)} 行, {len(df_original.columns)} 列")
    
    # 加载插值后数据
    df_interpolated = pd.read_csv('138x_8_3_interpolated.csv', header=None)
    print(f"插值后数据: {len(df_interpolated)} 行, {len(df_interpolated.columns)} 列")
    
    return df_original, df_interpolated

def analyze_missing_patterns(df_original, target_column=1):
    """分析缺失值的分布模式"""
    print(f"\n=== 第{target_column+1}列缺失值模式分析 ===")
    
    # 识别缺失值位置
    missing_mask = df_original.iloc[:, target_column].isna() | (df_original.iloc[:, target_column] == '')
    missing_indices = missing_mask[missing_mask].index.tolist()
    
    print(f"缺失值总数: {len(missing_indices)}")
    print(f"缺失值比例: {len(missing_indices)/len(df_original)*100:.2f}%")
    
    # 分析缺失值的连续性
    consecutive_groups = []
    if missing_indices:
        current_group = [missing_indices[0]]
        
        for i in range(1, len(missing_indices)):
            if missing_indices[i] - missing_indices[i-1] == 1:
                current_group.append(missing_indices[i])
            else:
                consecutive_groups.append(current_group)
                current_group = [missing_indices[i]]
        consecutive_groups.append(current_group)
        
        print(f"连续缺失组数: {len(consecutive_groups)}")
        group_sizes = [len(group) for group in consecutive_groups]
        print(f"连续缺失长度统计: 最小{min(group_sizes)}, 最大{max(group_sizes)}, 平均{np.mean(group_sizes):.1f}")
        
        # 分析缺失值间隔
        if len(missing_indices) > 1:
            intervals = [missing_indices[i] - missing_indices[i-1] for i in range(1, len(missing_indices))]
            single_missing = [interval for interval in intervals if interval > 1]
            if single_missing:
                print(f"单独缺失值间隔统计: 最小{min(single_missing)}, 最大{max(single_missing)}, 平均{np.mean(single_missing):.1f}")
    
    return missing_indices

def evaluate_interpolation_accuracy(df_original, df_interpolated, missing_indices, target_column=1):
    """评估插值准确性"""
    print(f"\n=== 插值准确性评估 ===")
    
    # 获取原始有效数据
    original_data = pd.to_numeric(df_original.iloc[:, target_column], errors='coerce')
    interpolated_data = pd.to_numeric(df_interpolated.iloc[:, target_column], errors='coerce')
    
    # 获取插值数据
    interpolated_values = interpolated_data.iloc[missing_indices]
    original_valid_data = original_data.dropna()
    
    print(f"原始有效数据统计:")
    print(f"  数量: {len(original_valid_data)}")
    print(f"  均值: {original_valid_data.mean():.3f}")
    print(f"  标准差: {original_valid_data.std():.3f}")
    print(f"  范围: [{original_valid_data.min():.3f}, {original_valid_data.max():.3f}]")
    
    print(f"\n插值数据统计:")
    print(f"  数量: {len(interpolated_values)}")
    print(f"  均值: {interpolated_values.mean():.3f}")
    print(f"  标准差: {interpolated_values.std():.3f}")
    print(f"  范围: [{interpolated_values.min():.3f}, {interpolated_values.max():.3f}]")
    
    # 统计检验
    print(f"\n统计检验:")
    
    # 1. 检查插值是否在合理范围内
    out_of_range_count = ((interpolated_values < original_valid_data.min()) | 
                         (interpolated_values > original_valid_data.max())).sum()
    print(f"  超出原始数据范围的插值: {out_of_range_count} ({out_of_range_count/len(interpolated_values)*100:.1f}%)")
    
    # 2. 分布相似性检验 (Kolmogorov-Smirnov test)
    try:
        ks_stat, ks_p_value = stats.ks_2samp(original_valid_data, interpolated_values)
        print(f"  K-S检验统计量: {ks_stat:.4f}, p值: {ks_p_value:.4f}")
        if ks_p_value > 0.05:
            print(f"  ✓ 插值数据与原始数据分布无显著差异 (p > 0.05)")
        else:
            print(f"  ⚠ 插值数据与原始数据分布存在显著差异 (p ≤ 0.05)")
    except Exception as e:
        print(f"  K-S检验失败: {e}")
    
    # 3. 均值差异检验 (t-test)
    try:
        t_stat, t_p_value = stats.ttest_ind(original_valid_data, interpolated_values)
        print(f"  t检验统计量: {t_stat:.4f}, p值: {t_p_value:.4f}")
        if t_p_value > 0.05:
            print(f"  ✓ 插值数据与原始数据均值无显著差异 (p > 0.05)")
        else:
            print(f"  ⚠ 插值数据与原始数据均值存在显著差异 (p ≤ 0.05)")
    except Exception as e:
        print(f"  t检验失败: {e}")
    
    return original_valid_data, interpolated_values

def check_temporal_consistency(df_interpolated, missing_indices, target_column=1):
    """检查时间序列的一致性"""
    print(f"\n=== 时间序列一致性检查 ===")
    
    data = pd.to_numeric(df_interpolated.iloc[:, target_column], errors='coerce')
    
    # 计算一阶差分
    diff_data = data.diff().dropna()
    
    print(f"数据变化统计:")
    print(f"  一阶差分均值: {diff_data.mean():.4f}")
    print(f"  一阶差分标准差: {diff_data.std():.4f}")
    print(f"  一阶差分范围: [{diff_data.min():.4f}, {diff_data.max():.4f}]")
    
    # 检查插值点的变化是否异常
    interpolated_diffs = []
    for idx in missing_indices:
        if idx > 0 and idx < len(data) - 1:
            # 计算插值点前后的变化
            before_diff = data.iloc[idx] - data.iloc[idx-1] if not pd.isna(data.iloc[idx-1]) else np.nan
            after_diff = data.iloc[idx+1] - data.iloc[idx] if not pd.isna(data.iloc[idx+1]) else np.nan
            
            if not pd.isna(before_diff):
                interpolated_diffs.append(before_diff)
            if not pd.isna(after_diff):
                interpolated_diffs.append(after_diff)
    
    if interpolated_diffs:
        interpolated_diffs = np.array(interpolated_diffs)
        print(f"\n插值点变化统计:")
        print(f"  插值点变化均值: {interpolated_diffs.mean():.4f}")
        print(f"  插值点变化标准差: {interpolated_diffs.std():.4f}")
        print(f"  插值点变化范围: [{interpolated_diffs.min():.4f}, {interpolated_diffs.max():.4f}]")
        
        # 检查异常变化
        threshold = diff_data.std() * 3  # 3倍标准差阈值
        abnormal_changes = np.abs(interpolated_diffs) > threshold
        print(f"  异常变化数量: {abnormal_changes.sum()} ({abnormal_changes.sum()/len(interpolated_diffs)*100:.1f}%)")

def generate_quality_report(df_original, df_interpolated):
    """生成数据质量报告"""
    print(f"\n=== 数据质量综合报告 ===")
    
    # 完整性检查
    total_cells = len(df_original) * len(df_original.columns)
    original_missing = 0
    interpolated_missing = 0
    
    for col in range(len(df_original.columns)):
        col_original_missing = df_original.iloc[:, col].isna().sum() + (df_original.iloc[:, col] == '').sum()
        col_interpolated_missing = df_interpolated.iloc[:, col].isna().sum() + (df_interpolated.iloc[:, col] == '').sum()
        original_missing += col_original_missing
        interpolated_missing += col_interpolated_missing
        
        if col_original_missing > 0:
            print(f"第{col+1}列: {col_original_missing} -> {col_interpolated_missing} (改善 {col_original_missing - col_interpolated_missing})")
    
    original_completeness = (total_cells - original_missing) / total_cells * 100
    interpolated_completeness = (total_cells - interpolated_missing) / total_cells * 100
    
    print(f"\n数据完整性:")
    print(f"  原始数据完整性: {original_completeness:.2f}%")
    print(f"  插值后完整性: {interpolated_completeness:.2f}%")
    print(f"  完整性提升: {interpolated_completeness - original_completeness:.2f}%")
    
    # 数据质量评级
    if interpolated_completeness >= 99.5:
        quality_grade = "优秀"
    elif interpolated_completeness >= 95:
        quality_grade = "良好"
    elif interpolated_completeness >= 90:
        quality_grade = "一般"
    else:
        quality_grade = "需改进"
    
    print(f"\n数据质量评级: {quality_grade}")
    
    # 插值效果评估
    target_column = 1
    missing_count = (df_original.iloc[:, target_column].isna() | (df_original.iloc[:, target_column] == '')).sum()
    interpolated_count = missing_count - (df_interpolated.iloc[:, target_column].isna() | (df_interpolated.iloc[:, target_column] == '')).sum()
    
    print(f"\n插值效果:")
    print(f"  成功插值数量: {interpolated_count}")
    print(f"  插值成功率: {interpolated_count/missing_count*100:.1f}%")
    
    return {
        'original_completeness': original_completeness,
        'interpolated_completeness': interpolated_completeness,
        'quality_grade': quality_grade,
        'interpolated_count': interpolated_count,
        'missing_count': missing_count
    }

def main():
    """主函数"""
    print("数据质量评估开始...")
    print("=" * 50)
    
    # 加载数据
    df_original, df_interpolated = load_and_compare_data()
    
    # 分析缺失值模式
    missing_indices = analyze_missing_patterns(df_original, target_column=1)
    
    # 评估插值准确性
    original_data, interpolated_values = evaluate_interpolation_accuracy(
        df_original, df_interpolated, missing_indices, target_column=1
    )
    
    # 检查时间序列一致性
    check_temporal_consistency(df_interpolated, missing_indices, target_column=1)
    
    # 生成质量报告
    quality_report = generate_quality_report(df_original, df_interpolated)
    
    print("\n" + "=" * 50)
    print("数据质量评估完成！")
    
    # 保存评估结果
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_filename = f"data_quality_report_{timestamp}.txt"
    
    with open(report_filename, 'w', encoding='utf-8') as f:
        f.write("数据质量评估报告\n")
        f.write("=" * 30 + "\n\n")
        f.write(f"评估时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"原始数据文件: 138x_8_3.csv\n")
        f.write(f"插值后文件: 138x_8_3_interpolated.csv\n\n")
        f.write(f"数据完整性: {quality_report['original_completeness']:.2f}% -> {quality_report['interpolated_completeness']:.2f}%\n")
        f.write(f"质量评级: {quality_report['quality_grade']}\n")
        f.write(f"插值成功: {quality_report['interpolated_count']}/{quality_report['missing_count']}\n")
    
    print(f"详细报告已保存至: {report_filename}")

if __name__ == "__main__":
    main()
