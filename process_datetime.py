#!/usr/bin/env python3
"""
处理CSV文件中的Date_Time列，去除年月日，只保留时分秒
输出文件名格式：WT + 原文件名
"""
import pandas as pd
import sys
from pathlib import Path

def process_datetime_file(input_file):
    """
    处理单个CSV文件，去除Date_Time列的年月日部分
    """
    input_path = Path(input_file)
    
    # 检查文件是否存在
    if not input_path.exists():
        print(f"错误：文件 {input_file} 不存在")
        return False
    
    print(f"处理文件: {input_file}")
    
    try:
        # 读取CSV文件
        df = pd.read_csv(input_file)
        print(f"读取了 {len(df)} 行数据")
        
        # 检查是否有Date_Time列
        if 'Date_Time' not in df.columns:
            print("错误：文件中没有找到 Date_Time 列")
            print(f"文件中的列名: {list(df.columns)}")
            return False
        
        # 显示原始数据示例
        print("原始Date_Time格式示例:")
        print(df['Date_Time'].head(3).tolist())
        
        # 处理Date_Time列，只保留时分秒
        df['Date_Time'] = pd.to_datetime(df['Date_Time']).dt.strftime('%H:%M:%S')
        
        # 显示处理后的数据示例
        print("处理后Date_Time格式示例:")
        print(df['Date_Time'].head(3).tolist())
        
        # 生成输出文件名：WT + 原文件名
        output_filename = f"WT{input_path.name}"
        output_path = input_path.parent / output_filename
        
        # 保存处理后的数据
        df.to_csv(output_path, index=False)
        
        print(f"✅ 处理完成！")
        print(f"✅ 输入文件: {input_file}")
        print(f"✅ 输出文件: {output_path}")
        print(f"✅ 处理了 {len(df)} 行数据")
        print(f"✅ Date_Time格式: 年月日时分秒 → 时分秒")
        
        return True
        
    except Exception as e:
        print(f"错误：处理文件时出现异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def process_multiple_files(file_list):
    """
    批量处理多个CSV文件
    """
    success_count = 0
    total_count = len(file_list)
    
    print(f"开始批量处理 {total_count} 个文件...")
    print("=" * 50)
    
    for i, file_path in enumerate(file_list, 1):
        print(f"\n[{i}/{total_count}] 处理文件: {file_path}")
        
        if process_datetime_file(file_path):
            success_count += 1
        
        print("-" * 30)
    
    print(f"\n📊 批量处理完成！")
    print(f"📊 成功处理: {success_count}/{total_count} 个文件")
    
    if success_count < total_count:
        print(f"⚠️  失败文件数: {total_count - success_count}")

def main():
    """
    主函数 - 支持单文件和多文件处理
    """
    if len(sys.argv) < 2:
        # 默认处理当前目录下的1.csv文件
        default_file = "1.csv"
        if Path(default_file).exists():
            print(f"未指定文件，使用默认文件: {default_file}")
            process_datetime_file(default_file)
        else:
            print("用法:")
            print("  python process_datetime.py <文件名>")
            print("  python process_datetime.py <文件1> <文件2> <文件3> ...")
            print("  或者将1.csv放在当前目录下直接运行")
            return
    else:
        # 处理命令行指定的文件
        file_list = sys.argv[1:]
        
        if len(file_list) == 1:
            # 单文件处理
            process_datetime_file(file_list[0])
        else:
            # 多文件批量处理
            process_multiple_files(file_list)

if __name__ == "__main__":
    main()
