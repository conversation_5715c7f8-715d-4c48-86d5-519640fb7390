#!/usr/bin/env python3
"""
数据完整性处理工具 - 简化版
确保每秒都有数据点，缺失数据用指定方法填充 带时间段的处理的
"""
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

def process_complete_data(input_file, output_file=None, fill_method='zero', 
                         start_time=None, end_time=None):
    """
    处理数据确保完整性，每秒一个数据点
    
    参数:
    - input_file: 输入CSV文件路径
    - output_file: 输出CSV文件路径（可选）
    - fill_method: 填充方法
      * 'empty': 用空值填充（推荐，明确标识缺失）
      * 'zero': 用0填充
      * 'interpolate': 线性插值（适合平滑数据）
      * 'forward_fill': 前向填充（使用前一个值）
      * 'backward_fill': 后向填充（使用后一个值）
    - start_time: 开始时间，格式'H:M:S'（可选，自动检测）
    - end_time: 结束时间，格式'H:M:S'（可选，自动检测）
    
    返回: 完整的DataFrame
    """
    
    print(f"📊 开始处理数据文件: {input_file}")
    print(f"🔧 填充方法: {fill_method}")
    
    # 读取数据
    df = pd.read_csv(input_file)
    df['Date_Time'] = df['Date_Time'].astype(str)
    
    # 自动检测时间范围
    if start_time is None or end_time is None:
        time_objects = []
        for time_str in df['Date_Time']:
            try:
                time_obj = datetime.strptime(time_str, '%H:%M:%S')
                time_objects.append(time_obj)
            except:
                continue
        
        if time_objects:
            start_time = min(time_objects).strftime('%H:%M:%S')
            end_time = max(time_objects).strftime('%H:%M:%S')
        else:
            raise ValueError("❌ 无法解析时间数据")
    
    print(f"⏰ 时间范围: {start_time} - {end_time}")
    
    # 提取每秒的最佳数据点
    df['time_second'] = df['Date_Time'].str[:8]
    grouped = df.groupby('time_second')
    
    existing_data = {}
    for time_sec, group in grouped:
        if len(group) == 1:
            row = group.iloc[0]
            existing_data[time_sec] = {
                'Date_Time': row['Date_Time'],
                'Tension': float(row['Tension']),
                'Speed': float(row['Speed']),
                'depth': float(row['depth'])
            }
        else:
            # 选择最接近中位数的点
            group_sorted = group.sort_values('Date_Time')
            tension_median = group_sorted['Tension'].median()
            speed_median = group_sorted['Speed'].median()
            depth_median = group_sorted['depth'].median()
            
            distances = []
            for idx, row in group_sorted.iterrows():
                dist = np.sqrt((row['Tension'] - tension_median)**2 + 
                             (row['Speed'] - speed_median)**2 + 
                             (row['depth'] - depth_median)**2)
                distances.append((dist, idx))
            
            distances.sort()
            best_row = group_sorted.loc[distances[0][1]]
            existing_data[time_sec] = {
                'Date_Time': best_row['Date_Time'],
                'Tension': float(best_row['Tension']),
                'Speed': float(best_row['Speed']),
                'depth': float(best_row['depth'])
            }
    
    # 生成完整时间序列
    start_dt = datetime.strptime(start_time, '%H:%M:%S')
    end_dt = datetime.strptime(end_time, '%H:%M:%S')
    
    complete_timeline = []
    current = start_dt
    while current <= end_dt:
        time_str = current.strftime('%H:%M:%S')
        if time_str.startswith('0'):
            time_str = time_str[1:]
        complete_timeline.append(time_str)
        current += timedelta(seconds=1)
    
    print(f"📈 理论数据点: {len(complete_timeline)}")
    print(f"✅ 实际数据点: {len(existing_data)}")
    print(f"🔧 需要填补: {len(complete_timeline) - len(existing_data)} 个")
    
    # 创建完整数据集
    complete_data = []
    filled_count = 0
    
    for time_point in complete_timeline:
        if time_point in existing_data:
            complete_data.append(existing_data[time_point])
        else:
            # 填充缺失数据
            filled_row = fill_missing_data(time_point, existing_data, 
                                         complete_timeline, fill_method)
            complete_data.append(filled_row)
            filled_count += 1
    
    # 创建DataFrame
    result_df = pd.DataFrame(complete_data)
    result_df = result_df[['Date_Time', 'Tension', 'Speed', 'depth']]
    
    print(f"🎯 最终数据点: {len(result_df)}")
    print(f"🔧 填补数据点: {filled_count}")
    
    # 保存文件
    if output_file:
        result_df.to_csv(output_file, index=False)
        print(f"💾 已保存到: {output_file}")
    
    return result_df

def fill_missing_data(time_point, existing_data, timeline, method):
    """填充缺失数据点"""

    if method == 'empty':
        return {
            'Date_Time': time_point,
            'Tension': '',
            'Speed': '',
            'depth': ''
        }

    elif method == 'zero':
        return {
            'Date_Time': time_point,
            'Tension': 0.0,
            'Speed': 0.0,
            'depth': 0.0
        }

    elif method == 'interpolate':
        return interpolate_point(time_point, existing_data, timeline)

    elif method == 'forward_fill':
        return forward_fill_point(time_point, existing_data, timeline)

    elif method == 'backward_fill':
        return backward_fill_point(time_point, existing_data, timeline)

    else:
        # 默认空值填充
        return {
            'Date_Time': time_point,
            'Tension': '',
            'Speed': '',
            'depth': ''
        }

def interpolate_point(time_point, existing_data, timeline):
    """线性插值"""
    time_idx = timeline.index(time_point)
    
    # 找前后有效数据点
    prev_data = None
    next_data = None
    prev_idx = next_idx = None
    
    # 向前找
    for i in range(time_idx - 1, -1, -1):
        if timeline[i] in existing_data:
            prev_data = existing_data[timeline[i]]
            prev_idx = i
            break
    
    # 向后找
    for i in range(time_idx + 1, len(timeline)):
        if timeline[i] in existing_data:
            next_data = existing_data[timeline[i]]
            next_idx = i
            break
    
    if prev_data and next_data:
        # 双向插值
        weight = (time_idx - prev_idx) / (next_idx - prev_idx)
        return {
            'Date_Time': time_point,
            'Tension': prev_data['Tension'] + weight * (next_data['Tension'] - prev_data['Tension']),
            'Speed': prev_data['Speed'] + weight * (next_data['Speed'] - prev_data['Speed']),
            'depth': prev_data['depth'] + weight * (next_data['depth'] - prev_data['depth'])
        }
    elif prev_data:
        return {
            'Date_Time': time_point,
            'Tension': prev_data['Tension'],
            'Speed': prev_data['Speed'],
            'depth': prev_data['depth']
        }
    elif next_data:
        return {
            'Date_Time': time_point,
            'Tension': next_data['Tension'],
            'Speed': next_data['Speed'],
            'depth': next_data['depth']
        }
    else:
        return {
            'Date_Time': time_point,
            'Tension': '',
            'Speed': '',
            'depth': ''
        }

def forward_fill_point(time_point, existing_data, timeline):
    """前向填充"""
    time_idx = timeline.index(time_point)
    
    for i in range(time_idx - 1, -1, -1):
        if timeline[i] in existing_data:
            data = existing_data[timeline[i]]
            return {
                'Date_Time': time_point,
                'Tension': data['Tension'],
                'Speed': data['Speed'],
                'depth': data['depth']
            }
    
    return {
        'Date_Time': time_point,
        'Tension': '',
        'Speed': '',
        'depth': ''
    }

def backward_fill_point(time_point, existing_data, timeline):
    """后向填充"""
    time_idx = timeline.index(time_point)
    
    for i in range(time_idx + 1, len(timeline)):
        if timeline[i] in existing_data:
            data = existing_data[timeline[i]]
            return {
                'Date_Time': time_point,
                'Tension': data['Tension'],
                'Speed': data['Speed'],
                'depth': data['depth']
            }
    
    return {
        'Date_Time': time_point,
        'Tension': '',
        'Speed': '',
        'depth': ''
    }

if __name__ == '__main__':
    # 使用示例
    input_file = '/Users/<USER>/Desktop/数据整理/datadeal/西41-117-135X（9-3）.csv'
    
    print("=" * 60)
    print("🚀 数据完整性处理工具")
    print("=" * 60)
    
    # 推荐使用零填充方法
    output_file = '135x空.csv'
    
    result = process_complete_data(
        input_file=input_file,
        output_file=output_file,
        fill_method='empty',  # 推荐使用空值填充
        start_time='2:45:12',
        end_time='3:51:57'
    )
    
    print("\n" + "=" * 60)
    print("✅ 处理完成！")
    print(f"📊 数据点数量: {len(result)} (应为4006)")
    print(f"💾 输出文件: {output_file}")
    print("=" * 60)
