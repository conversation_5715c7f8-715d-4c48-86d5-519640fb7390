#!/usr/bin/env python3
"""
修复CSV文件的数据类型问题，确保csv_merger_cli.py能正常处理
"""
import pandas as pd
import sys
from pathlib import Path

def fix_csv_file(input_file, output_file=None):
    """
    修复CSV文件的数据类型问题
    """
    if output_file is None:
        output_file = input_file
    
    print(f"修复文件: {input_file}")
    
    try:
        # 读取CSV文件
        df = pd.read_csv(input_file)
        print(f"  原始数据: {len(df)} 行")
        
        # 确保Date_Time列是字符串类型
        if 'Date_Time' in df.columns:
            df['Date_Time'] = df['Date_Time'].astype(str)
        
        # 确保数值列是数值类型
        numeric_cols = ['X_Acceler', 'Y_Acceler', 'Z_Acceler']
        for col in numeric_cols:
            if col in df.columns:
                # 转换为数值类型，无法转换的设为NaN
                df[col] = pd.to_numeric(df[col], errors='coerce')
                # 将NaN替换为空字符串（如果需要）
                # df[col] = df[col].fillna('')
        
        # 保存修复后的文件
        df.to_csv(output_file, index=False)
        print(f"  修复完成: {output_file}")
        
        return True
        
    except Exception as e:
        print(f"  错误: {e}")
        return False

def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("用法: python fix_csv_types.py <文件1> [文件2] [文件3] ...")
        return
    
    files = sys.argv[1:]
    success_count = 0
    
    for file_path in files:
        if Path(file_path).exists():
            if fix_csv_file(file_path):
                success_count += 1
        else:
            print(f"文件不存在: {file_path}")
    
    print(f"\n修复完成: {success_count}/{len(files)} 个文件")

if __name__ == "__main__":
    main()
