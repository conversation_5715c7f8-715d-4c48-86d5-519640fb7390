#!/usr/bin/env python3
"""
时间格式转换脚本
将CSV文件中的"时间"列改为"Date_Time"，并将时间格式从完整日期时间改为只保留时分秒
"""
import pandas as pd
import sys
from pathlib import Path

def convert_time_format(input_file, output_file=None):
    """
    转换CSV文件的时间格式
    """
    if output_file is None:
        # 如果没有指定输出文件，在原文件名后加_converted
        input_path = Path(input_file)
        output_file = input_path.parent / f"{input_path.stem}_converteds{input_path.suffix}"
    
    print(f"处理文件: {input_file}")
    
    try:
        # 读取CSV文件
        df = pd.read_csv(input_file)
        print(f"  原始数据: {len(df)} 行 {len(df.columns)} 列")
        
        # 显示原始列名
        print(f"  原始列名: {list(df.columns)}")
        
        # 查找时间列
        time_column = None
        if '时间' in df.columns:
            time_column = '时间'
        elif 'Date_Time' in df.columns:
            time_column = 'Date_Time'
        elif '时间戳' in df.columns:
            time_column = '时间戳'
        else:
            # 尝试查找包含时间数据的列
            for col in df.columns:
                if df[col].dtype == 'object':
                    # 检查前几行是否包含时间格式数据
                    sample_values = df[col].dropna().head(5)
                    for val in sample_values:
                        val_str = str(val)
                        if ':' in val_str and len(val_str) > 5:
                            time_column = col
                            break
                    if time_column:
                        break
        
        if not time_column:
            print("  错误: 未找到时间列")
            return False
        
        print(f"  找到时间列: {time_column}")
        
        # 显示原始时间格式示例
        sample_times = df[time_column].dropna().head(3)
        print("  原始时间格式示例:")
        for i, time_val in enumerate(sample_times):
            print(f"    {i+1}: {time_val}")
        
        # 转换时间格式
        def extract_time_only(time_str):
            """提取时分秒部分"""
            if pd.isna(time_str) or str(time_str).strip() == '':
                return ''
            
            time_str = str(time_str).strip()
            
            # 如果已经是HH:MM:SS格式，直接返回
            if len(time_str) == 8 and time_str.count(':') == 2:
                return time_str
            
            # 如果是完整的日期时间格式，提取时分秒部分
            try:
                # 尝试解析为datetime
                dt = pd.to_datetime(time_str)
                return dt.strftime('%H:%M:%S')
            except:
                # 如果解析失败，尝试直接提取时分秒部分
                if ' ' in time_str:
                    # 格式如 "2023-08-11 11:08:25"
                    parts = time_str.split(' ')
                    if len(parts) >= 2:
                        time_part = parts[1]
                        if ':' in time_part:
                            return time_part[:8]  # 取前8个字符 HH:MM:SS
                elif len(time_str) >= 8 and ':' in time_str:
                    # 直接是时间格式
                    return time_str[:8]
                
                # 如果都失败了，返回原值
                return time_str
        
        # 应用时间转换
        df[time_column] = df[time_column].apply(extract_time_only)
        
        # 将列名改为Date_Time
        if time_column != 'Date_Time':
            df = df.rename(columns={time_column: 'Date_Time'})
            print(f"  列名已改为: Date_Time")
        
        # 显示转换后的时间格式示例
        sample_converted = df['Date_Time'].dropna().head(3)
        print("  转换后时间格式示例:")
        for i, time_val in enumerate(sample_converted):
            print(f"    {i+1}: {time_val}")
        
        # 保存转换后的文件
        df.to_csv(output_file, index=False)
        
        print(f"  ✅ 转换完成!")
        print(f"  ✅ 输入文件: {input_file}")
        print(f"  ✅ 输出文件: {output_file}")
        print(f"  ✅ 处理了 {len(df)} 行数据")
        print(f"  ✅ 时间格式: 完整日期时间 → 时分秒")
        
        return True
        
    except Exception as e:
        print(f"  错误: 处理文件时出现异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def batch_convert_files(file_list):
    """
    批量转换多个文件
    """
    success_count = 0
    total_count = len(file_list)
    
    print(f"开始批量转换 {total_count} 个文件...")
    print("=" * 60)
    
    for i, file_path in enumerate(file_list, 1):
        print(f"\n[{i}/{total_count}] 处理文件: {file_path}")
        
        if Path(file_path).exists():
            if convert_time_format(file_path):
                success_count += 1
        else:
            print(f"  错误: 文件 {file_path} 不存在")
        
        print("-" * 40)
    
    print(f"\n📊 批量转换完成！")
    print(f"📊 成功转换: {success_count}/{total_count} 个文件")
    
    if success_count < total_count:
        print(f"⚠️  失败文件数: {total_count - success_count}")

def main():
    """
    主函数
    """
    if len(sys.argv) < 2:
        # 默认处理当前目录下的华200_4_13_wt空.csv文件
        default_file = "华200_4_13_wt空.csv"
        if Path(default_file).exists():
            print(f"未指定文件，使用默认文件: {default_file}")
            convert_time_format(default_file)
        else:
            print("用法:")
            print("  python convert_time_format.py <文件名>")
            print("  python convert_time_format.py <文件1> <文件2> <文件3> ...")
            print("  python convert_time_format.py <输入文件> <输出文件>")
            print("  或者将华200_4_13_wt空.csv放在当前目录下直接运行")
            return
    else:
        file_list = sys.argv[1:]
        
        if len(file_list) == 1:
            # 单文件处理
            convert_time_format(file_list[0])
        elif len(file_list) == 2 and not Path(file_list[1]).exists():
            # 指定输入和输出文件
            convert_time_format(file_list[0], file_list[1])
        else:
            # 多文件批量处理
            batch_convert_files(file_list)

if __name__ == "__main__":
    main()
