#!/usr/bin/env python3
"""
高性能传感器数据处理工具 - tests.csv格式输出版
支持时间序列对齐、关键点提取和并行处理优化
"""
import pandas as pd
import numpy as np
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor
import warnings
import csv
import time
warnings.filterwarnings('ignore')

def parse_excel_file(file_path):
    """
    高性能解析Excel文件，提取6个传感器数据
    """
    print(f"读取文件: {file_path}")
    
    # 检测文件格式
    try:
        with open(file_path, 'rb') as f:
            file_header = f.read(4)
        is_excel = (file_header == b'PK\x03\x04')
    except:
        is_excel = False
    
    file_ext = Path(file_path).suffix.lower()
    
    if is_excel or file_ext in ['.xlsx', '.xls']:
        # 读取Excel文件
        df = pd.read_excel(file_path, header=None)
        print(f"成功读取Excel文件，共 {len(df)} 行 {len(df.columns)} 列")
    else:
        # 读取CSV文件
        encodings = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig', 'latin1']
        df = None
        for encoding in encodings:
            try:
                df = pd.read_csv(file_path, header=None, encoding=encoding)
                print(f"成功使用 {encoding} 编码读取CSV文件")
                break
            except:
                continue
        
        if df is None:
            raise ValueError(f"无法读取文件 {file_path}")
    
    # 解析传感器数据
    sensors_data = {}
    
    # 从第3行开始处理数据（跳过标题行）
    for row_idx in range(2, len(df)):
        row_data = df.iloc[row_idx]
        
        # 解析6个传感器的数据
        sensor_columns = [
            (0, 1, 2, 3),    # WT1: 时间、X、Y、Z
            (5, 6, 7, 8),    # WT2
            (10, 11, 12, 13), # WT3
            (15, 16, 17, 18), # WT4
            (20, 21, 22, 23), # WT5
            (25, 26, 27, 28)  # WT6
        ]
        
        for sensor_idx, (time_col, x_col, y_col, z_col) in enumerate(sensor_columns):
            sensor_name = f"WT{sensor_idx + 1}"
            
            if z_col < len(row_data):
                time_val = row_data.iloc[time_col] if time_col < len(row_data) else None
                x_val = row_data.iloc[x_col] if x_col < len(row_data) else None
                y_val = row_data.iloc[y_col] if y_col < len(row_data) else None
                z_val = row_data.iloc[z_col] if z_col < len(row_data) else None
                
                # 检查数据有效性
                if (time_val is not None and not pd.isna(time_val) and 
                    x_val is not None and not pd.isna(x_val) and
                    y_val is not None and not pd.isna(y_val) and
                    z_val is not None and not pd.isna(z_val)):
                    
                    if sensor_name not in sensors_data:
                        sensors_data[sensor_name] = []
                    
                    # 处理时间格式
                    if hasattr(time_val, 'strftime'):
                        time_str = time_val.strftime('%Y-%m-%d %H:%M:%S')
                    else:
                        time_str = str(time_val)
                    
                    try:
                        sensors_data[sensor_name].append({
                            'datetime': pd.to_datetime(time_str),
                            'time_str': time_str,
                            'X': float(x_val),
                            'Y': float(y_val),
                            'Z': float(z_val)
                        })
                    except (ValueError, TypeError):
                        continue
    
    # 转换为DataFrame并排序
    sensor_dfs = {}
    for sensor_name, data in sensors_data.items():
        if data:
            df = pd.DataFrame(data)
            df = df.sort_values('datetime').reset_index(drop=True)
            sensor_dfs[sensor_name] = df
            print(f"  {sensor_name}: {len(df)} 行数据")
    
    return sensor_dfs

def process_sensor_alignment_optimized(args):
    """
    优化的并行传感器对齐处理
    """
    sensor_name, sensor_df, time_range = args
    
    print(f"对齐传感器 {sensor_name}...")
    
    # 创建时间索引以提高查找效率
    sensor_df_indexed = sensor_df.set_index('datetime')
    
    aligned_data = []
    
    # 批量处理时间点
    for time_point in time_range:
        second_start = time_point
        second_end = time_point + pd.Timedelta(seconds=1)
        
        # 查找该秒内的数据
        mask = (sensor_df_indexed.index >= second_start) & (sensor_df_indexed.index < second_end)
        second_data = sensor_df_indexed[mask]
        
        if not second_data.empty:
            if len(second_data) == 1:
                best_row = second_data.iloc[0]
            else:
                # 向量化计算中位数和距离
                x_median = second_data['X'].median()
                y_median = second_data['Y'].median()
                z_median = second_data['Z'].median()
                
                distances = np.sqrt((second_data['X'] - x_median)**2 + 
                                  (second_data['Y'] - y_median)**2 + 
                                  (second_data['Z'] - z_median)**2)
                
                best_idx = distances.idxmin()
                best_row = second_data.loc[best_idx]
            
            aligned_data.append({
                'time_str': time_point.strftime('%Y-%m-%d %H:%M:%S'),
                'X': best_row['X'],
                'Y': best_row['Y'],
                'Z': best_row['Z']
            })
        else:
            aligned_data.append({
                'time_str': time_point.strftime('%Y-%m-%d %H:%M:%S'),
                'X': '',
                'Y': '',
                'Z': ''
            })
    
    return sensor_name, aligned_data

def merge_sensors_tests_format(sensor_dfs, output_path):
    """
    高性能合并传感器数据，输出tests.csv格式
    """
    print("开始高性能时间序列对齐和数据合并...")
    start_time = time.time()
    
    # 确定全局时间范围
    global_start = None
    global_end = None
    
    for sensor_name, df in sensor_dfs.items():
        if not df.empty:
            sensor_start = df['datetime'].min()
            sensor_end = df['datetime'].max()
            print(f"{sensor_name}: 时间范围 {sensor_start} 到 {sensor_end}")
            
            if global_start is None or sensor_start < global_start:
                global_start = sensor_start
            if global_end is None or sensor_end > global_end:
                global_end = sensor_end
    
    print(f"全局时间范围: {global_start} 到 {global_end}")
    
    # 生成完整时间序列
    time_range = pd.date_range(start=global_start.replace(microsecond=0),
                              end=global_end.replace(microsecond=0),
                              freq='1S')
    
    print(f"生成完整时间序列: {len(time_range)} 个时间点")
    
    # 并行处理传感器对齐
    process_args = [(sensor_name, df, time_range) for sensor_name, df in sensor_dfs.items()]
    
    aligned_sensors = {}
    with ThreadPoolExecutor(max_workers=min(6, len(process_args))) as executor:
        results = list(executor.map(process_sensor_alignment_optimized, process_args))
    
    for sensor_name, aligned_data in results:
        aligned_sensors[sensor_name] = aligned_data
        print(f"  {sensor_name} 对齐完成: {len(aligned_data)} 个时间点")
    
    # 生成tests.csv格式输出
    print("生成tests.csv格式输出...")
    
    output_rows = []
    
    # 添加标题行
    header1 = ['WT1', '', '', '', '', 'WT2', '', '', '', '', 'WT3', '', '', '', '', 
               'WT4', '', '', '', '', 'WT5', '', '', '', '', 'WT6', '', '', '']
    header2 = ['时间', 'X', 'Y', 'Z', '', '时间', 'X', 'Y', 'Z', '', '时间', 'X', 'Y', 'Z', '',
               '时间', 'X', 'Y', 'Z', '', '时间', 'X', 'Y', 'Z', '', '时间', 'X', 'Y', 'Z']
    
    output_rows.append(header1)
    output_rows.append(header2)
    
    # 批量处理数据行
    batch_size = 1000
    for batch_start in range(0, len(time_range), batch_size):
        batch_end = min(batch_start + batch_size, len(time_range))
        
        if batch_start % (batch_size * 5) == 0:
            print(f"  进度: {batch_start}/{len(time_range)}")
        
        for i in range(batch_start, batch_end):
            row = []
            
            # 按WT1到WT6顺序添加数据
            for wt_index in range(1, 7):
                sensor_name = f"WT{wt_index}"
                
                if sensor_name in aligned_sensors and i < len(aligned_sensors[sensor_name]):
                    data = aligned_sensors[sensor_name][i]
                    row.extend([data['time_str'], data['X'], data['Y'], data['Z'], ''])
                else:
                    row.extend(['', '', '', '', ''])
            
            # 移除最后一个空列
            if row and row[-1] == '':
                row = row[:-1]
            
            output_rows.append(row)
    
    # 高性能写入文件
    print("保存数据到文件...")
    with open(output_path, 'w', encoding='utf-8', newline='') as f:
        writer = csv.writer(f)
        writer.writerows(output_rows)
    
    elapsed_time = time.time() - start_time
    print(f"\n✅ 处理完成！")
    print(f"✅ 处理了6个传感器的数据")
    print(f"✅ 时间序列对齐并提取了{len(time_range)}个关键数据点")
    print(f"✅ 输出格式：tests.csv格式")
    print(f"✅ 使用并行处理优化性能")
    print(f"✅ 处理时间：{elapsed_time:.2f}秒")
    print(f"✅ 输出文件：{output_path}")
    
    return len(time_range)

def main():
    """主函数"""
    input_file = '/Users/<USER>/Desktop/5口井的完整数据/23年绞车振动与张力数据/113-8-13/8-13.csv'
    output_file = '/Users/<USER>/Desktop/5口井的完整数据/23年绞车振动与张力数据/113-8-13/8-13_tests_format.csv'
    
    print(f"开始高性能处理 {Path(input_file).name}...")
    
    try:
        # 1. 解析Excel/CSV文件
        sensor_dfs = parse_excel_file(input_file)
        
        if not sensor_dfs:
            print("错误：未能解析到有效的传感器数据")
            return
        
        # 2. 时间序列对齐和数据合并
        total_points = merge_sensors_tests_format(sensor_dfs, output_file)
        
        print(f"\n🎉 处理完成！输出文件：{output_file}")
        
    except Exception as e:
        print(f"错误：{str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
