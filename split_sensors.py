#!/usr/bin/env python3
"""
将Excel文件中的6个传感器数据拆分成独立的CSV文件
自动识别WT1-WT6传感器，并以传感器名称命名输出文件
"""
import pandas as pd
import numpy as np
from pathlib import Path
import sys
import warnings
warnings.filterwarnings('ignore')

def detect_sensor_columns(df):
    """
    检测Excel文件中的传感器列位置
    返回每个传感器的列索引字典
    """
    sensor_info = {}
    
    # 查找第一行中的WT1-WT6标识
    first_row = df.iloc[0].fillna('').astype(str).str.upper()
    
    for i, cell_value in enumerate(first_row):
        # 查找WT1到WT6
        for wt_num in range(1, 7):
            wt_name = f"WT{wt_num}"
            if wt_name in cell_value:
                # 找到传感器标识，记录起始列
                sensor_info[wt_name] = {
                    'start_col': i,
                    'time_col': i,
                    'x_col': i + 1,
                    'y_col': i + 2,
                    'z_col': i + 3
                }
                print(f"找到 {wt_name}: 列 {i}-{i+3} (时间,X,Y,Z)")
                break
    
    return sensor_info

def extract_sensor_data(df, sensor_info, sensor_name):
    """
    提取单个传感器的数据
    """
    if sensor_name not in sensor_info:
        print(f"警告：未找到传感器 {sensor_name}")
        return None
    
    cols = sensor_info[sensor_name]
    
    # 提取数据（从第3行开始，跳过标题行）
    sensor_data = []
    
    for row_idx in range(2, len(df)):  # 从第3行开始
        row = df.iloc[row_idx]
        
        # 检查列索引是否有效
        if (cols['z_col'] < len(row) and 
            not pd.isna(row.iloc[cols['time_col']]) and
            not pd.isna(row.iloc[cols['x_col']]) and
            not pd.isna(row.iloc[cols['y_col']]) and
            not pd.isna(row.iloc[cols['z_col']])):
            
            try:
                time_val = row.iloc[cols['time_col']]
                x_val = float(row.iloc[cols['x_col']])
                y_val = float(row.iloc[cols['y_col']])
                z_val = float(row.iloc[cols['z_col']])
                
                # 处理时间格式
                if hasattr(time_val, 'strftime'):
                    time_str = time_val.strftime('%Y-%m-%d %H:%M:%S')
                else:
                    time_str = str(time_val)
                
                sensor_data.append({
                    'Date_Time': time_str,
                    'X_Acceler': x_val,
                    'Y_Acceler': y_val,
                    'Z_Acceler': z_val
                })
                
            except (ValueError, TypeError):
                # 跳过无法转换的数据
                continue
    
    if sensor_data:
        return pd.DataFrame(sensor_data)
    else:
        return None

def split_excel_to_csv(input_file, output_dir=None):
    """
    将Excel文件拆分成6个传感器的CSV文件
    """
    input_path = Path(input_file)
    
    if not input_path.exists():
        print(f"错误：文件 {input_file} 不存在")
        return False
    
    if output_dir is None:
        output_dir = input_path.parent
    else:
        output_dir = Path(output_dir)
        output_dir.mkdir(exist_ok=True)
    
    print(f"处理文件: {input_file}")
    
    try:
        # 读取Excel文件
        if input_path.suffix.lower() in ['.xlsx', '.xls']:
            df = pd.read_excel(input_file, header=None)
        else:
            # 尝试作为CSV读取
            encodings = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig', 'latin1']
            df = None
            for encoding in encodings:
                try:
                    df = pd.read_csv(input_file, header=None, encoding=encoding)
                    break
                except:
                    continue
            
            if df is None:
                raise ValueError("无法读取文件")
        
        print(f"读取了 {len(df)} 行 {len(df.columns)} 列数据")
        
        # 显示前几行以便调试
        print("\n文件前3行内容:")
        for i in range(min(3, len(df))):
            print(f"第{i+1}行: {df.iloc[i].fillna('').astype(str).tolist()[:10]}...")
        
        # 检测传感器列位置
        sensor_info = detect_sensor_columns(df)
        
        if not sensor_info:
            print("错误：未找到任何传感器标识 (WT1-WT6)")
            return False
        
        print(f"\n找到 {len(sensor_info)} 个传感器")
        
        # 拆分每个传感器的数据
        success_count = 0
        
        for sensor_name in ['WT1', 'WT2', 'WT3', 'WT4', 'WT5', 'WT6']:
            print(f"\n处理传感器 {sensor_name}...")
            
            sensor_df = extract_sensor_data(df, sensor_info, sensor_name)
            
            if sensor_df is not None and not sensor_df.empty:
                # 生成输出文件名
                output_file = output_dir / f"{sensor_name}.csv"
                
                # 保存CSV文件
                sensor_df.to_csv(output_file, index=False)
                
                print(f"  ✅ {sensor_name}: {len(sensor_df)} 行数据 → {output_file}")
                success_count += 1
            else:
                print(f"  ❌ {sensor_name}: 未找到有效数据")
        
        print(f"\n📊 拆分完成！")
        print(f"📊 成功拆分: {success_count}/6 个传感器")
        print(f"📊 输出目录: {output_dir}")
        
        return success_count > 0
        
    except Exception as e:
        print(f"错误：处理文件时出现异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """
    主函数
    """
    if len(sys.argv) < 2:
        print("用法:")
        print("  python split_sensors.py <Excel文件路径> [输出目录]")
        print("  例如: python split_sensors.py data.xlsx")
        print("  例如: python split_sensors.py data.xlsx ./output/")
        return
    
    input_file = sys.argv[1]
    output_dir = sys.argv[2] if len(sys.argv) > 2 else None
    
    print("🚀 Excel传感器数据拆分工具")
    print("=" * 50)
    
    success = split_excel_to_csv(input_file, output_dir)
    
    if success:
        print("\n🎉 拆分成功完成！")
        print("每个传感器的数据已保存为独立的CSV文件")
        print("文件格式: Date_Time, X_Acceler, Y_Acceler, Z_Acceler")
    else:
        print("\n❌ 拆分失败，请检查文件格式和内容")

if __name__ == "__main__":
    main()
