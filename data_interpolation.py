#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
相邻波动率插值法补全CSV数据中的缺失值
只针对数据点为空的值进行插值，已存在的值不修改
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

def calculate_volatility_interpolation(data, window_size=5):
    """
    使用相邻波动率插值法计算缺失值
    
    参数:
    data: 包含缺失值的数据序列
    window_size: 计算波动率的窗口大小
    
    返回:
    插值后的数据序列
    """
    result = data.copy()
    
    for i in range(len(data)):
        if pd.isna(data.iloc[i]) or data.iloc[i] == '':
            # 找到前后的有效数据点
            left_values = []
            right_values = []
            
            # 向左查找有效值
            for j in range(i-1, max(-1, i-window_size-1), -1):
                if not pd.isna(data.iloc[j]) and data.iloc[j] != '':
                    try:
                        left_values.append(float(data.iloc[j]))
                    except (ValueError, TypeError):
                        continue
                if len(left_values) >= window_size:
                    break
            
            # 向右查找有效值
            for j in range(i+1, min(len(data), i+window_size+1)):
                if not pd.isna(data.iloc[j]) and data.iloc[j] != '':
                    try:
                        right_values.append(float(data.iloc[j]))
                    except (ValueError, TypeError):
                        continue
                if len(right_values) >= window_size:
                    break
            
            # 如果左右都有足够的数据点，进行插值
            if len(left_values) >= 2 and len(right_values) >= 2:
                # 计算左侧和右侧的波动率
                left_volatility = np.std(left_values) if len(left_values) > 1 else 0
                right_volatility = np.std(right_values) if len(right_values) > 1 else 0
                
                # 计算左侧和右侧的均值
                left_mean = np.mean(left_values)
                right_mean = np.mean(right_values)
                
                # 根据波动率加权插值
                if left_volatility + right_volatility > 0:
                    # 波动率越小，权重越大
                    left_weight = 1 / (left_volatility + 1e-8)
                    right_weight = 1 / (right_volatility + 1e-8)
                    
                    total_weight = left_weight + right_weight
                    interpolated_value = (left_mean * left_weight + right_mean * right_weight) / total_weight
                else:
                    # 如果波动率都为0，使用简单平均
                    interpolated_value = (left_mean + right_mean) / 2
                
                result.iloc[i] = round(interpolated_value, 2)
            
            # 如果只有一侧有数据，使用该侧的均值
            elif len(left_values) >= 2:
                result.iloc[i] = round(np.mean(left_values), 2)
            elif len(right_values) >= 2:
                result.iloc[i] = round(np.mean(right_values), 2)
            
            # 如果左右都只有一个值，使用线性插值
            elif len(left_values) == 1 and len(right_values) == 1:
                result.iloc[i] = round((left_values[0] + right_values[0]) / 2, 2)
    
    return result

def evaluate_interpolation_quality(original_data, interpolated_data, column_name):
    """
    评估插值质量
    """
    print(f"\n=== {column_name} 列插值质量评估 ===")
    
    # 统计缺失值数量
    original_missing = original_data.isna().sum() + (original_data == '').sum()
    interpolated_missing = interpolated_data.isna().sum() + (interpolated_data == '').sum()
    
    print(f"原始缺失值数量: {original_missing}")
    print(f"插值后缺失值数量: {interpolated_missing}")
    print(f"成功插值数量: {original_missing - interpolated_missing}")
    
    # 转换为数值类型进行统计分析
    original_numeric = pd.to_numeric(original_data, errors='coerce')
    interpolated_numeric = pd.to_numeric(interpolated_data, errors='coerce')
    
    # 原始数据的统计信息
    original_valid = original_numeric.dropna()
    interpolated_valid = interpolated_numeric.dropna()
    
    if len(original_valid) > 0:
        print(f"\n原始数据统计:")
        print(f"  均值: {original_valid.mean():.3f}")
        print(f"  标准差: {original_valid.std():.3f}")
        print(f"  最小值: {original_valid.min():.3f}")
        print(f"  最大值: {original_valid.max():.3f}")
        
        print(f"\n插值后数据统计:")
        print(f"  均值: {interpolated_valid.mean():.3f}")
        print(f"  标准差: {interpolated_valid.std():.3f}")
        print(f"  最小值: {interpolated_valid.min():.3f}")
        print(f"  最大值: {interpolated_valid.max():.3f}")
        
        # 计算插值值的统计信息
        mask = (original_data.isna() | (original_data == '')) & interpolated_data.notna()
        if mask.sum() > 0:
            interpolated_values = interpolated_numeric[mask]
            print(f"\n插值数据统计:")
            print(f"  均值: {interpolated_values.mean():.3f}")
            print(f"  标准差: {interpolated_values.std():.3f}")
            print(f"  最小值: {interpolated_values.min():.3f}")
            print(f"  最大值: {interpolated_values.max():.3f}")
            
            # 检查插值是否在合理范围内
            original_range = original_valid.max() - original_valid.min()
            interpolated_range = interpolated_values.max() - interpolated_values.min()
            
            print(f"\n合理性检查:")
            print(f"  原始数据范围: {original_range:.3f}")
            print(f"  插值数据范围: {interpolated_range:.3f}")
            
            # 检查插值是否超出原始数据范围
            out_of_range = ((interpolated_values < original_valid.min()) | 
                           (interpolated_values > original_valid.max())).sum()
            print(f"  超出原始范围的插值数量: {out_of_range} ({out_of_range/len(interpolated_values)*100:.1f}%)")

def main():
    """
    主函数：读取数据，进行插值，保存结果
    """
    # 读取CSV文件
    print("正在读取数据文件...")
    try:
        df = pd.read_csv('/Users/<USER>/Desktop/数据整理/datadeal/135xwt空.csv', header=None)
        print(f"成功读取数据，共 {len(df)} 行，{len(df.columns)} 列")
    except Exception as e:
        print(f"读取文件失败: {e}")
        return
    
    # 备份原始数据
    df_original = df.copy()
    
    # 检查第二列（索引为1）的缺失情况
    column_to_interpolate = 1  # 第二列
    print(f"\n检查第 {column_to_interpolate + 1} 列的数据情况...")
    
    missing_count = df.iloc[:, column_to_interpolate].isna().sum() + (df.iloc[:, column_to_interpolate] == '').sum()
    total_count = len(df)
    print(f"缺失值数量: {missing_count} / {total_count} ({missing_count/total_count*100:.1f}%)")
    
    if missing_count == 0:
        print("没有发现缺失值，无需插值")
        return
    
    # 进行插值
    print("\n开始进行相邻波动率插值...")
    df.iloc[:, column_to_interpolate] = calculate_volatility_interpolation(
        df.iloc[:, column_to_interpolate], window_size=5
    )
    
    # 评估插值质量
    evaluate_interpolation_quality(
        df_original.iloc[:, column_to_interpolate], 
        df.iloc[:, column_to_interpolate], 
        f"第{column_to_interpolate + 1}列"
    )
    
    # 保存插值后的数据
    output_filename = '/Users/<USER>/Desktop/5口井的完整数据/截/135x完整空_interpolated.csv'
    print(f"\n保存插值后的数据到 {output_filename}...")
    df.to_csv(output_filename, header=False, index=False)
    print("数据保存完成！")
    
    # 生成对比报告
    print("\n=== 数据完整性报告 ===")
    print("插值前后对比:")
    for col in range(len(df.columns)):
        original_missing = df_original.iloc[:, col].isna().sum() + (df_original.iloc[:, col] == '').sum()
        current_missing = df.iloc[:, col].isna().sum() + (df.iloc[:, col] == '').sum()
        if original_missing > 0:
            print(f"  第{col+1}列: {original_missing} -> {current_missing} (减少 {original_missing - current_missing})")
    
    print(f"\n总体完整性: {((total_count * len(df.columns) - df.isna().sum().sum() - (df == '').sum().sum()) / (total_count * len(df.columns)) * 100):.2f}%")

if __name__ == "__main__":
    main()
